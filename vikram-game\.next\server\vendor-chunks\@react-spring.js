"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-spring";
exports.ids = ["vendor-chunks/@react-spring"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Animated: () => (/* binding */ Animated),\n/* harmony export */   AnimatedArray: () => (/* binding */ AnimatedArray),\n/* harmony export */   AnimatedObject: () => (/* binding */ AnimatedObject),\n/* harmony export */   AnimatedString: () => (/* binding */ AnimatedString),\n/* harmony export */   AnimatedValue: () => (/* binding */ AnimatedValue),\n/* harmony export */   createHost: () => (/* binding */ createHost),\n/* harmony export */   getAnimated: () => (/* binding */ getAnimated),\n/* harmony export */   getAnimatedType: () => (/* binding */ getAnimatedType),\n/* harmony export */   getPayload: () => (/* binding */ getPayload),\n/* harmony export */   isAnimated: () => (/* binding */ isAnimated),\n/* harmony export */   setAnimated: () => (/* binding */ setAnimated)\n/* harmony export */ });\n/* harmony import */ var _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-spring/shared */ \"(ssr)/./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/Animated.ts\n\nvar $node = Symbol.for(\"Animated:node\");\nvar isAnimated = (value) => !!value && value[$node] === value;\nvar getAnimated = (owner) => owner && owner[$node];\nvar setAnimated = (owner, node) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.defineHidden)(owner, $node, node);\nvar getPayload = (owner) => owner && owner[$node] && owner[$node].getPayload();\nvar Animated = class {\n  constructor() {\n    setAnimated(this, this);\n  }\n  /** Get every `AnimatedValue` used by this node. */\n  getPayload() {\n    return this.payload || [];\n  }\n};\n\n// src/AnimatedValue.ts\n\nvar AnimatedValue = class _AnimatedValue extends Animated {\n  constructor(_value) {\n    super();\n    this._value = _value;\n    this.done = true;\n    this.durationProgress = 0;\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.num(this._value)) {\n      this.lastPosition = this._value;\n    }\n  }\n  /** @internal */\n  static create(value) {\n    return new _AnimatedValue(value);\n  }\n  getPayload() {\n    return [this];\n  }\n  getValue() {\n    return this._value;\n  }\n  setValue(value, step) {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.num(value)) {\n      this.lastPosition = value;\n      if (step) {\n        value = Math.round(value / step) * step;\n        if (this.done) {\n          this.lastPosition = value;\n        }\n      }\n    }\n    if (this._value === value) {\n      return false;\n    }\n    this._value = value;\n    return true;\n  }\n  reset() {\n    const { done } = this;\n    this.done = false;\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.num(this._value)) {\n      this.elapsedTime = 0;\n      this.durationProgress = 0;\n      this.lastPosition = this._value;\n      if (done) this.lastVelocity = null;\n      this.v0 = null;\n    }\n  }\n};\n\n// src/AnimatedString.ts\n\nvar AnimatedString = class _AnimatedString extends AnimatedValue {\n  constructor(value) {\n    super(0);\n    this._string = null;\n    this._toString = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.createInterpolator)({\n      output: [value, value]\n    });\n  }\n  /** @internal */\n  static create(value) {\n    return new _AnimatedString(value);\n  }\n  getValue() {\n    const value = this._string;\n    return value == null ? this._string = this._toString(this._value) : value;\n  }\n  setValue(value) {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.str(value)) {\n      if (value == this._string) {\n        return false;\n      }\n      this._string = value;\n      this._value = 1;\n    } else if (super.setValue(value)) {\n      this._string = null;\n    } else {\n      return false;\n    }\n    return true;\n  }\n  reset(goal) {\n    if (goal) {\n      this._toString = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.createInterpolator)({\n        output: [this.getValue(), goal]\n      });\n    }\n    this._value = 0;\n    super.reset();\n  }\n};\n\n// src/AnimatedArray.ts\n\n\n// src/AnimatedObject.ts\n\n\n// src/context.ts\nvar TreeContext = { dependencies: null };\n\n// src/AnimatedObject.ts\nvar AnimatedObject = class extends Animated {\n  constructor(source) {\n    super();\n    this.source = source;\n    this.setValue(source);\n  }\n  getValue(animated) {\n    const values = {};\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(this.source, (source, key) => {\n      if (isAnimated(source)) {\n        values[key] = source.getValue(animated);\n      } else if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(source)) {\n        values[key] = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(source);\n      } else if (!animated) {\n        values[key] = source;\n      }\n    });\n    return values;\n  }\n  /** Replace the raw object data */\n  setValue(source) {\n    this.source = source;\n    this.payload = this._makePayload(source);\n  }\n  reset() {\n    if (this.payload) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(this.payload, (node) => node.reset());\n    }\n  }\n  /** Create a payload set. */\n  _makePayload(source) {\n    if (source) {\n      const payload = /* @__PURE__ */ new Set();\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(source, this._addToPayload, payload);\n      return Array.from(payload);\n    }\n  }\n  /** Add to a payload set. */\n  _addToPayload(source) {\n    if (TreeContext.dependencies && (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(source)) {\n      TreeContext.dependencies.add(source);\n    }\n    const payload = getPayload(source);\n    if (payload) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(payload, (node) => this.add(node));\n    }\n  }\n};\n\n// src/AnimatedArray.ts\nvar AnimatedArray = class _AnimatedArray extends AnimatedObject {\n  constructor(source) {\n    super(source);\n  }\n  /** @internal */\n  static create(source) {\n    return new _AnimatedArray(source);\n  }\n  getValue() {\n    return this.source.map((node) => node.getValue());\n  }\n  setValue(source) {\n    const payload = this.getPayload();\n    if (source.length == payload.length) {\n      return payload.map((node, i) => node.setValue(source[i])).some(Boolean);\n    }\n    super.setValue(source.map(makeAnimated));\n    return true;\n  }\n};\nfunction makeAnimated(value) {\n  const nodeType = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isAnimatedString)(value) ? AnimatedString : AnimatedValue;\n  return nodeType.create(value);\n}\n\n// src/getAnimatedType.ts\n\nfunction getAnimatedType(value) {\n  const parentNode = getAnimated(value);\n  return parentNode ? parentNode.constructor : _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(value) ? AnimatedArray : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isAnimatedString)(value) ? AnimatedString : AnimatedValue;\n}\n\n// src/createHost.ts\n\n\n// src/withAnimated.tsx\n\n\n\nvar withAnimated = (Component, host) => {\n  const hasInstance = (\n    // Function components must use \"forwardRef\" to avoid being\n    // re-rendered on every animation frame.\n    !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(Component) || Component.prototype && Component.prototype.isReactComponent\n  );\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((givenProps, givenRef) => {\n    const instanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const ref = hasInstance && // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(\n      (value) => {\n        instanceRef.current = updateRef(givenRef, value);\n      },\n      [givenRef]\n    );\n    const [props, deps] = getAnimatedState(givenProps, host);\n    const forceUpdate = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useForceUpdate)();\n    const callback = () => {\n      const instance = instanceRef.current;\n      if (hasInstance && !instance) {\n        return;\n      }\n      const didUpdate = instance ? host.applyAnimatedValues(instance, props.getValue(true)) : false;\n      if (didUpdate === false) {\n        forceUpdate();\n      }\n    };\n    const observer = new PropsObserver(callback, deps);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(void 0);\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n      observerRef.current = observer;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(deps, (dep) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.addFluidObserver)(dep, observer));\n      return () => {\n        if (observerRef.current) {\n          (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(\n            observerRef.current.deps,\n            (dep) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.removeFluidObserver)(dep, observerRef.current)\n          );\n          _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.cancel(observerRef.current.update);\n        }\n      };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(callback, []);\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useOnce)(() => () => {\n      const observer2 = observerRef.current;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(observer2.deps, (dep) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.removeFluidObserver)(dep, observer2));\n    });\n    const usedProps = host.getComponentProps(props.getValue());\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Component, { ...usedProps, ref });\n  });\n};\nvar PropsObserver = class {\n  constructor(update, deps) {\n    this.update = update;\n    this.deps = deps;\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.write(this.update);\n    }\n  }\n};\nfunction getAnimatedState(props, host) {\n  const dependencies = /* @__PURE__ */ new Set();\n  TreeContext.dependencies = dependencies;\n  if (props.style)\n    props = {\n      ...props,\n      style: host.createAnimatedStyle(props.style)\n    };\n  props = new AnimatedObject(props);\n  TreeContext.dependencies = null;\n  return [props, dependencies];\n}\nfunction updateRef(ref, value) {\n  if (ref) {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(ref)) ref(value);\n    else ref.current = value;\n  }\n  return value;\n}\n\n// src/createHost.ts\nvar cacheKey = Symbol.for(\"AnimatedComponent\");\nvar createHost = (components, {\n  applyAnimatedValues = () => false,\n  createAnimatedStyle = (style) => new AnimatedObject(style),\n  getComponentProps = (props) => props\n} = {}) => {\n  const hostConfig = {\n    applyAnimatedValues,\n    createAnimatedStyle,\n    getComponentProps\n  };\n  const animated = (Component) => {\n    const displayName = getDisplayName(Component) || \"Anonymous\";\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.str(Component)) {\n      Component = animated[Component] || (animated[Component] = withAnimated(Component, hostConfig));\n    } else {\n      Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));\n    }\n    Component.displayName = `Animated(${displayName})`;\n    return Component;\n  };\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(components, (Component, key) => {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(components)) {\n      key = getDisplayName(Component);\n    }\n    animated[key] = animated(Component);\n  });\n  return {\n    animated\n  };\n};\nvar getDisplayName = (arg) => _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.str(arg) ? arg : arg && _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.str(arg.displayName) ? arg.displayName : _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(arg) && arg.name || null;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXNwcmluZy9hbmltYXRlZC9kaXN0L3JlYWN0LXNwcmluZ19hbmltYXRlZC5tb2Rlcm4ubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNvRDtBQUNwRDtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsa0VBQVk7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDMEM7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxvREFBRTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLG9EQUFFO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CO0FBQ0EsUUFBUSxvREFBRTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDcUU7QUFDckU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsd0VBQWtCO0FBQ3ZDO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxvREFBRztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHdFQUFrQjtBQUN6QztBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ3dEOztBQUV4RDtBQU04Qjs7QUFFOUI7QUFDQSxvQkFBb0I7O0FBRXBCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksOERBQVE7QUFDWjtBQUNBO0FBQ0EsUUFBUSxTQUFTLG1FQUFhO0FBQzlCLHNCQUFzQixtRUFBYTtBQUNuQyxRQUFRO0FBQ1I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLDBEQUFJO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSw4REFBUTtBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsbUVBQWE7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLDBEQUFJO0FBQ1Y7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHNFQUFnQjtBQUNuQztBQUNBOztBQUVBO0FBQ3dGO0FBQ3hGO0FBQ0E7QUFDQSwrQ0FBK0Msb0RBQUcsOEJBQThCLHNFQUFpQjtBQUNqRzs7QUFFQTtBQUN3RTs7QUFFeEU7QUFDK0I7QUFDb0M7QUFVckM7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLG9EQUFHO0FBQ1I7QUFDQSxTQUFTLGlEQUFVO0FBQ25CLHdCQUF3Qiw2Q0FBTTtBQUM5QjtBQUNBLElBQUksa0RBQVc7QUFDZjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixvRUFBYztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLDZDQUFNO0FBQzlCLElBQUksK0VBQXlCO0FBQzdCO0FBQ0EsTUFBTSwwREFBSyxnQkFBZ0Isc0VBQWdCO0FBQzNDO0FBQ0E7QUFDQSxVQUFVLDBEQUFLO0FBQ2Y7QUFDQSxxQkFBcUIseUVBQW1CO0FBQ3hDO0FBQ0EsVUFBVSxxREFBRztBQUNiO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsSUFBSSxnREFBUztBQUNiLElBQUksNkRBQU87QUFDWDtBQUNBLE1BQU0sMERBQUssMEJBQTBCLHlFQUFtQjtBQUN4RCxLQUFLO0FBQ0w7QUFDQSwyQkFBMkIsZ0RBQW1CLGNBQWMsbUJBQW1CO0FBQy9FLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxxREFBRztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLG9EQUFHO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxJQUFJO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLG9EQUFHO0FBQ1g7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLHdDQUF3QyxZQUFZO0FBQ3BEO0FBQ0E7QUFDQSxFQUFFLDhEQUFTO0FBQ1gsUUFBUSxvREFBRztBQUNYO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixvREFBRyx5QkFBeUIsb0RBQUcsMENBQTBDLG9EQUFHO0FBYXhHIiwic291cmNlcyI6WyJEOlxcaWRsZXNjYXBlclxcdmlrcmFtLWdhbWVcXG5vZGVfbW9kdWxlc1xcQHJlYWN0LXNwcmluZ1xcYW5pbWF0ZWRcXGRpc3RcXHJlYWN0LXNwcmluZ19hbmltYXRlZC5tb2Rlcm4ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9BbmltYXRlZC50c1xuaW1wb3J0IHsgZGVmaW5lSGlkZGVuIH0gZnJvbSBcIkByZWFjdC1zcHJpbmcvc2hhcmVkXCI7XG52YXIgJG5vZGUgPSBTeW1ib2wuZm9yKFwiQW5pbWF0ZWQ6bm9kZVwiKTtcbnZhciBpc0FuaW1hdGVkID0gKHZhbHVlKSA9PiAhIXZhbHVlICYmIHZhbHVlWyRub2RlXSA9PT0gdmFsdWU7XG52YXIgZ2V0QW5pbWF0ZWQgPSAob3duZXIpID0+IG93bmVyICYmIG93bmVyWyRub2RlXTtcbnZhciBzZXRBbmltYXRlZCA9IChvd25lciwgbm9kZSkgPT4gZGVmaW5lSGlkZGVuKG93bmVyLCAkbm9kZSwgbm9kZSk7XG52YXIgZ2V0UGF5bG9hZCA9IChvd25lcikgPT4gb3duZXIgJiYgb3duZXJbJG5vZGVdICYmIG93bmVyWyRub2RlXS5nZXRQYXlsb2FkKCk7XG52YXIgQW5pbWF0ZWQgPSBjbGFzcyB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHNldEFuaW1hdGVkKHRoaXMsIHRoaXMpO1xuICB9XG4gIC8qKiBHZXQgZXZlcnkgYEFuaW1hdGVkVmFsdWVgIHVzZWQgYnkgdGhpcyBub2RlLiAqL1xuICBnZXRQYXlsb2FkKCkge1xuICAgIHJldHVybiB0aGlzLnBheWxvYWQgfHwgW107XG4gIH1cbn07XG5cbi8vIHNyYy9BbmltYXRlZFZhbHVlLnRzXG5pbXBvcnQgeyBpcyB9IGZyb20gXCJAcmVhY3Qtc3ByaW5nL3NoYXJlZFwiO1xudmFyIEFuaW1hdGVkVmFsdWUgPSBjbGFzcyBfQW5pbWF0ZWRWYWx1ZSBleHRlbmRzIEFuaW1hdGVkIHtcbiAgY29uc3RydWN0b3IoX3ZhbHVlKSB7XG4gICAgc3VwZXIoKTtcbiAgICB0aGlzLl92YWx1ZSA9IF92YWx1ZTtcbiAgICB0aGlzLmRvbmUgPSB0cnVlO1xuICAgIHRoaXMuZHVyYXRpb25Qcm9ncmVzcyA9IDA7XG4gICAgaWYgKGlzLm51bSh0aGlzLl92YWx1ZSkpIHtcbiAgICAgIHRoaXMubGFzdFBvc2l0aW9uID0gdGhpcy5fdmFsdWU7XG4gICAgfVxuICB9XG4gIC8qKiBAaW50ZXJuYWwgKi9cbiAgc3RhdGljIGNyZWF0ZSh2YWx1ZSkge1xuICAgIHJldHVybiBuZXcgX0FuaW1hdGVkVmFsdWUodmFsdWUpO1xuICB9XG4gIGdldFBheWxvYWQoKSB7XG4gICAgcmV0dXJuIFt0aGlzXTtcbiAgfVxuICBnZXRWYWx1ZSgpIHtcbiAgICByZXR1cm4gdGhpcy5fdmFsdWU7XG4gIH1cbiAgc2V0VmFsdWUodmFsdWUsIHN0ZXApIHtcbiAgICBpZiAoaXMubnVtKHZhbHVlKSkge1xuICAgICAgdGhpcy5sYXN0UG9zaXRpb24gPSB2YWx1ZTtcbiAgICAgIGlmIChzdGVwKSB7XG4gICAgICAgIHZhbHVlID0gTWF0aC5yb3VuZCh2YWx1ZSAvIHN0ZXApICogc3RlcDtcbiAgICAgICAgaWYgKHRoaXMuZG9uZSkge1xuICAgICAgICAgIHRoaXMubGFzdFBvc2l0aW9uID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgaWYgKHRoaXMuX3ZhbHVlID09PSB2YWx1ZSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICB0aGlzLl92YWx1ZSA9IHZhbHVlO1xuICAgIHJldHVybiB0cnVlO1xuICB9XG4gIHJlc2V0KCkge1xuICAgIGNvbnN0IHsgZG9uZSB9ID0gdGhpcztcbiAgICB0aGlzLmRvbmUgPSBmYWxzZTtcbiAgICBpZiAoaXMubnVtKHRoaXMuX3ZhbHVlKSkge1xuICAgICAgdGhpcy5lbGFwc2VkVGltZSA9IDA7XG4gICAgICB0aGlzLmR1cmF0aW9uUHJvZ3Jlc3MgPSAwO1xuICAgICAgdGhpcy5sYXN0UG9zaXRpb24gPSB0aGlzLl92YWx1ZTtcbiAgICAgIGlmIChkb25lKSB0aGlzLmxhc3RWZWxvY2l0eSA9IG51bGw7XG4gICAgICB0aGlzLnYwID0gbnVsbDtcbiAgICB9XG4gIH1cbn07XG5cbi8vIHNyYy9BbmltYXRlZFN0cmluZy50c1xuaW1wb3J0IHsgaXMgYXMgaXMyLCBjcmVhdGVJbnRlcnBvbGF0b3IgfSBmcm9tIFwiQHJlYWN0LXNwcmluZy9zaGFyZWRcIjtcbnZhciBBbmltYXRlZFN0cmluZyA9IGNsYXNzIF9BbmltYXRlZFN0cmluZyBleHRlbmRzIEFuaW1hdGVkVmFsdWUge1xuICBjb25zdHJ1Y3Rvcih2YWx1ZSkge1xuICAgIHN1cGVyKDApO1xuICAgIHRoaXMuX3N0cmluZyA9IG51bGw7XG4gICAgdGhpcy5fdG9TdHJpbmcgPSBjcmVhdGVJbnRlcnBvbGF0b3Ioe1xuICAgICAgb3V0cHV0OiBbdmFsdWUsIHZhbHVlXVxuICAgIH0pO1xuICB9XG4gIC8qKiBAaW50ZXJuYWwgKi9cbiAgc3RhdGljIGNyZWF0ZSh2YWx1ZSkge1xuICAgIHJldHVybiBuZXcgX0FuaW1hdGVkU3RyaW5nKHZhbHVlKTtcbiAgfVxuICBnZXRWYWx1ZSgpIHtcbiAgICBjb25zdCB2YWx1ZSA9IHRoaXMuX3N0cmluZztcbiAgICByZXR1cm4gdmFsdWUgPT0gbnVsbCA/IHRoaXMuX3N0cmluZyA9IHRoaXMuX3RvU3RyaW5nKHRoaXMuX3ZhbHVlKSA6IHZhbHVlO1xuICB9XG4gIHNldFZhbHVlKHZhbHVlKSB7XG4gICAgaWYgKGlzMi5zdHIodmFsdWUpKSB7XG4gICAgICBpZiAodmFsdWUgPT0gdGhpcy5fc3RyaW5nKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICAgIHRoaXMuX3N0cmluZyA9IHZhbHVlO1xuICAgICAgdGhpcy5fdmFsdWUgPSAxO1xuICAgIH0gZWxzZSBpZiAoc3VwZXIuc2V0VmFsdWUodmFsdWUpKSB7XG4gICAgICB0aGlzLl9zdHJpbmcgPSBudWxsO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xuICB9XG4gIHJlc2V0KGdvYWwpIHtcbiAgICBpZiAoZ29hbCkge1xuICAgICAgdGhpcy5fdG9TdHJpbmcgPSBjcmVhdGVJbnRlcnBvbGF0b3Ioe1xuICAgICAgICBvdXRwdXQ6IFt0aGlzLmdldFZhbHVlKCksIGdvYWxdXG4gICAgICB9KTtcbiAgICB9XG4gICAgdGhpcy5fdmFsdWUgPSAwO1xuICAgIHN1cGVyLnJlc2V0KCk7XG4gIH1cbn07XG5cbi8vIHNyYy9BbmltYXRlZEFycmF5LnRzXG5pbXBvcnQgeyBpc0FuaW1hdGVkU3RyaW5nIH0gZnJvbSBcIkByZWFjdC1zcHJpbmcvc2hhcmVkXCI7XG5cbi8vIHNyYy9BbmltYXRlZE9iamVjdC50c1xuaW1wb3J0IHtcbiAgZWFjaCxcbiAgZWFjaFByb3AsXG4gIGdldEZsdWlkVmFsdWUsXG4gIGhhc0ZsdWlkVmFsdWVcbn0gZnJvbSBcIkByZWFjdC1zcHJpbmcvc2hhcmVkXCI7XG5cbi8vIHNyYy9jb250ZXh0LnRzXG52YXIgVHJlZUNvbnRleHQgPSB7IGRlcGVuZGVuY2llczogbnVsbCB9O1xuXG4vLyBzcmMvQW5pbWF0ZWRPYmplY3QudHNcbnZhciBBbmltYXRlZE9iamVjdCA9IGNsYXNzIGV4dGVuZHMgQW5pbWF0ZWQge1xuICBjb25zdHJ1Y3Rvcihzb3VyY2UpIHtcbiAgICBzdXBlcigpO1xuICAgIHRoaXMuc291cmNlID0gc291cmNlO1xuICAgIHRoaXMuc2V0VmFsdWUoc291cmNlKTtcbiAgfVxuICBnZXRWYWx1ZShhbmltYXRlZCkge1xuICAgIGNvbnN0IHZhbHVlcyA9IHt9O1xuICAgIGVhY2hQcm9wKHRoaXMuc291cmNlLCAoc291cmNlLCBrZXkpID0+IHtcbiAgICAgIGlmIChpc0FuaW1hdGVkKHNvdXJjZSkpIHtcbiAgICAgICAgdmFsdWVzW2tleV0gPSBzb3VyY2UuZ2V0VmFsdWUoYW5pbWF0ZWQpO1xuICAgICAgfSBlbHNlIGlmIChoYXNGbHVpZFZhbHVlKHNvdXJjZSkpIHtcbiAgICAgICAgdmFsdWVzW2tleV0gPSBnZXRGbHVpZFZhbHVlKHNvdXJjZSk7XG4gICAgICB9IGVsc2UgaWYgKCFhbmltYXRlZCkge1xuICAgICAgICB2YWx1ZXNba2V5XSA9IHNvdXJjZTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICByZXR1cm4gdmFsdWVzO1xuICB9XG4gIC8qKiBSZXBsYWNlIHRoZSByYXcgb2JqZWN0IGRhdGEgKi9cbiAgc2V0VmFsdWUoc291cmNlKSB7XG4gICAgdGhpcy5zb3VyY2UgPSBzb3VyY2U7XG4gICAgdGhpcy5wYXlsb2FkID0gdGhpcy5fbWFrZVBheWxvYWQoc291cmNlKTtcbiAgfVxuICByZXNldCgpIHtcbiAgICBpZiAodGhpcy5wYXlsb2FkKSB7XG4gICAgICBlYWNoKHRoaXMucGF5bG9hZCwgKG5vZGUpID0+IG5vZGUucmVzZXQoKSk7XG4gICAgfVxuICB9XG4gIC8qKiBDcmVhdGUgYSBwYXlsb2FkIHNldC4gKi9cbiAgX21ha2VQYXlsb2FkKHNvdXJjZSkge1xuICAgIGlmIChzb3VyY2UpIHtcbiAgICAgIGNvbnN0IHBheWxvYWQgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICAgICAgZWFjaFByb3Aoc291cmNlLCB0aGlzLl9hZGRUb1BheWxvYWQsIHBheWxvYWQpO1xuICAgICAgcmV0dXJuIEFycmF5LmZyb20ocGF5bG9hZCk7XG4gICAgfVxuICB9XG4gIC8qKiBBZGQgdG8gYSBwYXlsb2FkIHNldC4gKi9cbiAgX2FkZFRvUGF5bG9hZChzb3VyY2UpIHtcbiAgICBpZiAoVHJlZUNvbnRleHQuZGVwZW5kZW5jaWVzICYmIGhhc0ZsdWlkVmFsdWUoc291cmNlKSkge1xuICAgICAgVHJlZUNvbnRleHQuZGVwZW5kZW5jaWVzLmFkZChzb3VyY2UpO1xuICAgIH1cbiAgICBjb25zdCBwYXlsb2FkID0gZ2V0UGF5bG9hZChzb3VyY2UpO1xuICAgIGlmIChwYXlsb2FkKSB7XG4gICAgICBlYWNoKHBheWxvYWQsIChub2RlKSA9PiB0aGlzLmFkZChub2RlKSk7XG4gICAgfVxuICB9XG59O1xuXG4vLyBzcmMvQW5pbWF0ZWRBcnJheS50c1xudmFyIEFuaW1hdGVkQXJyYXkgPSBjbGFzcyBfQW5pbWF0ZWRBcnJheSBleHRlbmRzIEFuaW1hdGVkT2JqZWN0IHtcbiAgY29uc3RydWN0b3Ioc291cmNlKSB7XG4gICAgc3VwZXIoc291cmNlKTtcbiAgfVxuICAvKiogQGludGVybmFsICovXG4gIHN0YXRpYyBjcmVhdGUoc291cmNlKSB7XG4gICAgcmV0dXJuIG5ldyBfQW5pbWF0ZWRBcnJheShzb3VyY2UpO1xuICB9XG4gIGdldFZhbHVlKCkge1xuICAgIHJldHVybiB0aGlzLnNvdXJjZS5tYXAoKG5vZGUpID0+IG5vZGUuZ2V0VmFsdWUoKSk7XG4gIH1cbiAgc2V0VmFsdWUoc291cmNlKSB7XG4gICAgY29uc3QgcGF5bG9hZCA9IHRoaXMuZ2V0UGF5bG9hZCgpO1xuICAgIGlmIChzb3VyY2UubGVuZ3RoID09IHBheWxvYWQubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gcGF5bG9hZC5tYXAoKG5vZGUsIGkpID0+IG5vZGUuc2V0VmFsdWUoc291cmNlW2ldKSkuc29tZShCb29sZWFuKTtcbiAgICB9XG4gICAgc3VwZXIuc2V0VmFsdWUoc291cmNlLm1hcChtYWtlQW5pbWF0ZWQpKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxufTtcbmZ1bmN0aW9uIG1ha2VBbmltYXRlZCh2YWx1ZSkge1xuICBjb25zdCBub2RlVHlwZSA9IGlzQW5pbWF0ZWRTdHJpbmcodmFsdWUpID8gQW5pbWF0ZWRTdHJpbmcgOiBBbmltYXRlZFZhbHVlO1xuICByZXR1cm4gbm9kZVR5cGUuY3JlYXRlKHZhbHVlKTtcbn1cblxuLy8gc3JjL2dldEFuaW1hdGVkVHlwZS50c1xuaW1wb3J0IHsgaXMgYXMgaXMzLCBpc0FuaW1hdGVkU3RyaW5nIGFzIGlzQW5pbWF0ZWRTdHJpbmcyIH0gZnJvbSBcIkByZWFjdC1zcHJpbmcvc2hhcmVkXCI7XG5mdW5jdGlvbiBnZXRBbmltYXRlZFR5cGUodmFsdWUpIHtcbiAgY29uc3QgcGFyZW50Tm9kZSA9IGdldEFuaW1hdGVkKHZhbHVlKTtcbiAgcmV0dXJuIHBhcmVudE5vZGUgPyBwYXJlbnROb2RlLmNvbnN0cnVjdG9yIDogaXMzLmFycih2YWx1ZSkgPyBBbmltYXRlZEFycmF5IDogaXNBbmltYXRlZFN0cmluZzIodmFsdWUpID8gQW5pbWF0ZWRTdHJpbmcgOiBBbmltYXRlZFZhbHVlO1xufVxuXG4vLyBzcmMvY3JlYXRlSG9zdC50c1xuaW1wb3J0IHsgaXMgYXMgaXM1LCBlYWNoUHJvcCBhcyBlYWNoUHJvcDIgfSBmcm9tIFwiQHJlYWN0LXNwcmluZy9zaGFyZWRcIjtcblxuLy8gc3JjL3dpdGhBbmltYXRlZC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgZm9yd2FyZFJlZiwgdXNlUmVmLCB1c2VDYWxsYmFjaywgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQge1xuICBpcyBhcyBpczQsXG4gIGVhY2ggYXMgZWFjaDIsXG4gIHJhZixcbiAgdXNlRm9yY2VVcGRhdGUsXG4gIHVzZU9uY2UsXG4gIGFkZEZsdWlkT2JzZXJ2ZXIsXG4gIHJlbW92ZUZsdWlkT2JzZXJ2ZXIsXG4gIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3Rcbn0gZnJvbSBcIkByZWFjdC1zcHJpbmcvc2hhcmVkXCI7XG52YXIgd2l0aEFuaW1hdGVkID0gKENvbXBvbmVudCwgaG9zdCkgPT4ge1xuICBjb25zdCBoYXNJbnN0YW5jZSA9IChcbiAgICAvLyBGdW5jdGlvbiBjb21wb25lbnRzIG11c3QgdXNlIFwiZm9yd2FyZFJlZlwiIHRvIGF2b2lkIGJlaW5nXG4gICAgLy8gcmUtcmVuZGVyZWQgb24gZXZlcnkgYW5pbWF0aW9uIGZyYW1lLlxuICAgICFpczQuZnVuKENvbXBvbmVudCkgfHwgQ29tcG9uZW50LnByb3RvdHlwZSAmJiBDb21wb25lbnQucHJvdG90eXBlLmlzUmVhY3RDb21wb25lbnRcbiAgKTtcbiAgcmV0dXJuIGZvcndhcmRSZWYoKGdpdmVuUHJvcHMsIGdpdmVuUmVmKSA9PiB7XG4gICAgY29uc3QgaW5zdGFuY2VSZWYgPSB1c2VSZWYobnVsbCk7XG4gICAgY29uc3QgcmVmID0gaGFzSW5zdGFuY2UgJiYgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL3J1bGVzLW9mLWhvb2tzXG4gICAgdXNlQ2FsbGJhY2soXG4gICAgICAodmFsdWUpID0+IHtcbiAgICAgICAgaW5zdGFuY2VSZWYuY3VycmVudCA9IHVwZGF0ZVJlZihnaXZlblJlZiwgdmFsdWUpO1xuICAgICAgfSxcbiAgICAgIFtnaXZlblJlZl1cbiAgICApO1xuICAgIGNvbnN0IFtwcm9wcywgZGVwc10gPSBnZXRBbmltYXRlZFN0YXRlKGdpdmVuUHJvcHMsIGhvc3QpO1xuICAgIGNvbnN0IGZvcmNlVXBkYXRlID0gdXNlRm9yY2VVcGRhdGUoKTtcbiAgICBjb25zdCBjYWxsYmFjayA9ICgpID0+IHtcbiAgICAgIGNvbnN0IGluc3RhbmNlID0gaW5zdGFuY2VSZWYuY3VycmVudDtcbiAgICAgIGlmIChoYXNJbnN0YW5jZSAmJiAhaW5zdGFuY2UpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgY29uc3QgZGlkVXBkYXRlID0gaW5zdGFuY2UgPyBob3N0LmFwcGx5QW5pbWF0ZWRWYWx1ZXMoaW5zdGFuY2UsIHByb3BzLmdldFZhbHVlKHRydWUpKSA6IGZhbHNlO1xuICAgICAgaWYgKGRpZFVwZGF0ZSA9PT0gZmFsc2UpIHtcbiAgICAgICAgZm9yY2VVcGRhdGUoKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IG9ic2VydmVyID0gbmV3IFByb3BzT2JzZXJ2ZXIoY2FsbGJhY2ssIGRlcHMpO1xuICAgIGNvbnN0IG9ic2VydmVyUmVmID0gdXNlUmVmKHZvaWQgMCk7XG4gICAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgICBvYnNlcnZlclJlZi5jdXJyZW50ID0gb2JzZXJ2ZXI7XG4gICAgICBlYWNoMihkZXBzLCAoZGVwKSA9PiBhZGRGbHVpZE9ic2VydmVyKGRlcCwgb2JzZXJ2ZXIpKTtcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGlmIChvYnNlcnZlclJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgZWFjaDIoXG4gICAgICAgICAgICBvYnNlcnZlclJlZi5jdXJyZW50LmRlcHMsXG4gICAgICAgICAgICAoZGVwKSA9PiByZW1vdmVGbHVpZE9ic2VydmVyKGRlcCwgb2JzZXJ2ZXJSZWYuY3VycmVudClcbiAgICAgICAgICApO1xuICAgICAgICAgIHJhZi5jYW5jZWwob2JzZXJ2ZXJSZWYuY3VycmVudC51cGRhdGUpO1xuICAgICAgICB9XG4gICAgICB9O1xuICAgIH0pO1xuICAgIHVzZUVmZmVjdChjYWxsYmFjaywgW10pO1xuICAgIHVzZU9uY2UoKCkgPT4gKCkgPT4ge1xuICAgICAgY29uc3Qgb2JzZXJ2ZXIyID0gb2JzZXJ2ZXJSZWYuY3VycmVudDtcbiAgICAgIGVhY2gyKG9ic2VydmVyMi5kZXBzLCAoZGVwKSA9PiByZW1vdmVGbHVpZE9ic2VydmVyKGRlcCwgb2JzZXJ2ZXIyKSk7XG4gICAgfSk7XG4gICAgY29uc3QgdXNlZFByb3BzID0gaG9zdC5nZXRDb21wb25lbnRQcm9wcyhwcm9wcy5nZXRWYWx1ZSgpKTtcbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIFJlYWN0LmNyZWF0ZUVsZW1lbnQoQ29tcG9uZW50LCB7IC4uLnVzZWRQcm9wcywgcmVmIH0pO1xuICB9KTtcbn07XG52YXIgUHJvcHNPYnNlcnZlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IodXBkYXRlLCBkZXBzKSB7XG4gICAgdGhpcy51cGRhdGUgPSB1cGRhdGU7XG4gICAgdGhpcy5kZXBzID0gZGVwcztcbiAgfVxuICBldmVudE9ic2VydmVkKGV2ZW50KSB7XG4gICAgaWYgKGV2ZW50LnR5cGUgPT0gXCJjaGFuZ2VcIikge1xuICAgICAgcmFmLndyaXRlKHRoaXMudXBkYXRlKTtcbiAgICB9XG4gIH1cbn07XG5mdW5jdGlvbiBnZXRBbmltYXRlZFN0YXRlKHByb3BzLCBob3N0KSB7XG4gIGNvbnN0IGRlcGVuZGVuY2llcyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KCk7XG4gIFRyZWVDb250ZXh0LmRlcGVuZGVuY2llcyA9IGRlcGVuZGVuY2llcztcbiAgaWYgKHByb3BzLnN0eWxlKVxuICAgIHByb3BzID0ge1xuICAgICAgLi4ucHJvcHMsXG4gICAgICBzdHlsZTogaG9zdC5jcmVhdGVBbmltYXRlZFN0eWxlKHByb3BzLnN0eWxlKVxuICAgIH07XG4gIHByb3BzID0gbmV3IEFuaW1hdGVkT2JqZWN0KHByb3BzKTtcbiAgVHJlZUNvbnRleHQuZGVwZW5kZW5jaWVzID0gbnVsbDtcbiAgcmV0dXJuIFtwcm9wcywgZGVwZW5kZW5jaWVzXTtcbn1cbmZ1bmN0aW9uIHVwZGF0ZVJlZihyZWYsIHZhbHVlKSB7XG4gIGlmIChyZWYpIHtcbiAgICBpZiAoaXM0LmZ1bihyZWYpKSByZWYodmFsdWUpO1xuICAgIGVsc2UgcmVmLmN1cnJlbnQgPSB2YWx1ZTtcbiAgfVxuICByZXR1cm4gdmFsdWU7XG59XG5cbi8vIHNyYy9jcmVhdGVIb3N0LnRzXG52YXIgY2FjaGVLZXkgPSBTeW1ib2wuZm9yKFwiQW5pbWF0ZWRDb21wb25lbnRcIik7XG52YXIgY3JlYXRlSG9zdCA9IChjb21wb25lbnRzLCB7XG4gIGFwcGx5QW5pbWF0ZWRWYWx1ZXMgPSAoKSA9PiBmYWxzZSxcbiAgY3JlYXRlQW5pbWF0ZWRTdHlsZSA9IChzdHlsZSkgPT4gbmV3IEFuaW1hdGVkT2JqZWN0KHN0eWxlKSxcbiAgZ2V0Q29tcG9uZW50UHJvcHMgPSAocHJvcHMpID0+IHByb3BzXG59ID0ge30pID0+IHtcbiAgY29uc3QgaG9zdENvbmZpZyA9IHtcbiAgICBhcHBseUFuaW1hdGVkVmFsdWVzLFxuICAgIGNyZWF0ZUFuaW1hdGVkU3R5bGUsXG4gICAgZ2V0Q29tcG9uZW50UHJvcHNcbiAgfTtcbiAgY29uc3QgYW5pbWF0ZWQgPSAoQ29tcG9uZW50KSA9PiB7XG4gICAgY29uc3QgZGlzcGxheU5hbWUgPSBnZXREaXNwbGF5TmFtZShDb21wb25lbnQpIHx8IFwiQW5vbnltb3VzXCI7XG4gICAgaWYgKGlzNS5zdHIoQ29tcG9uZW50KSkge1xuICAgICAgQ29tcG9uZW50ID0gYW5pbWF0ZWRbQ29tcG9uZW50XSB8fCAoYW5pbWF0ZWRbQ29tcG9uZW50XSA9IHdpdGhBbmltYXRlZChDb21wb25lbnQsIGhvc3RDb25maWcpKTtcbiAgICB9IGVsc2Uge1xuICAgICAgQ29tcG9uZW50ID0gQ29tcG9uZW50W2NhY2hlS2V5XSB8fCAoQ29tcG9uZW50W2NhY2hlS2V5XSA9IHdpdGhBbmltYXRlZChDb21wb25lbnQsIGhvc3RDb25maWcpKTtcbiAgICB9XG4gICAgQ29tcG9uZW50LmRpc3BsYXlOYW1lID0gYEFuaW1hdGVkKCR7ZGlzcGxheU5hbWV9KWA7XG4gICAgcmV0dXJuIENvbXBvbmVudDtcbiAgfTtcbiAgZWFjaFByb3AyKGNvbXBvbmVudHMsIChDb21wb25lbnQsIGtleSkgPT4ge1xuICAgIGlmIChpczUuYXJyKGNvbXBvbmVudHMpKSB7XG4gICAgICBrZXkgPSBnZXREaXNwbGF5TmFtZShDb21wb25lbnQpO1xuICAgIH1cbiAgICBhbmltYXRlZFtrZXldID0gYW5pbWF0ZWQoQ29tcG9uZW50KTtcbiAgfSk7XG4gIHJldHVybiB7XG4gICAgYW5pbWF0ZWRcbiAgfTtcbn07XG52YXIgZ2V0RGlzcGxheU5hbWUgPSAoYXJnKSA9PiBpczUuc3RyKGFyZykgPyBhcmcgOiBhcmcgJiYgaXM1LnN0cihhcmcuZGlzcGxheU5hbWUpID8gYXJnLmRpc3BsYXlOYW1lIDogaXM1LmZ1bihhcmcpICYmIGFyZy5uYW1lIHx8IG51bGw7XG5leHBvcnQge1xuICBBbmltYXRlZCxcbiAgQW5pbWF0ZWRBcnJheSxcbiAgQW5pbWF0ZWRPYmplY3QsXG4gIEFuaW1hdGVkU3RyaW5nLFxuICBBbmltYXRlZFZhbHVlLFxuICBjcmVhdGVIb3N0LFxuICBnZXRBbmltYXRlZCxcbiAgZ2V0QW5pbWF0ZWRUeXBlLFxuICBnZXRQYXlsb2FkLFxuICBpc0FuaW1hdGVkLFxuICBzZXRBbmltYXRlZFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-spring/core/dist/react-spring_core.modern.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/@react-spring/core/dist/react-spring_core.modern.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Any: () => (/* reexport safe */ _react_spring_types__WEBPACK_IMPORTED_MODULE_3__.Any),\n/* harmony export */   BailSignal: () => (/* binding */ BailSignal),\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   FrameValue: () => (/* binding */ FrameValue),\n/* harmony export */   Globals: () => (/* reexport safe */ _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals),\n/* harmony export */   Interpolation: () => (/* binding */ Interpolation),\n/* harmony export */   Spring: () => (/* binding */ Spring),\n/* harmony export */   SpringContext: () => (/* binding */ SpringContext),\n/* harmony export */   SpringRef: () => (/* binding */ SpringRef),\n/* harmony export */   SpringValue: () => (/* binding */ SpringValue),\n/* harmony export */   Trail: () => (/* binding */ Trail),\n/* harmony export */   Transition: () => (/* binding */ Transition),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   createInterpolator: () => (/* reexport safe */ _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.createInterpolator),\n/* harmony export */   easings: () => (/* reexport safe */ _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.easings),\n/* harmony export */   inferTo: () => (/* binding */ inferTo),\n/* harmony export */   interpolate: () => (/* binding */ interpolate),\n/* harmony export */   to: () => (/* binding */ to),\n/* harmony export */   update: () => (/* binding */ update),\n/* harmony export */   useChain: () => (/* binding */ useChain),\n/* harmony export */   useInView: () => (/* binding */ useInView),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* reexport safe */ _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect),\n/* harmony export */   useReducedMotion: () => (/* reexport safe */ _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useReducedMotion),\n/* harmony export */   useResize: () => (/* binding */ useResize),\n/* harmony export */   useScroll: () => (/* binding */ useScroll),\n/* harmony export */   useSpring: () => (/* binding */ useSpring),\n/* harmony export */   useSpringRef: () => (/* binding */ useSpringRef),\n/* harmony export */   useSpringValue: () => (/* binding */ useSpringValue),\n/* harmony export */   useSprings: () => (/* binding */ useSprings),\n/* harmony export */   useTrail: () => (/* binding */ useTrail),\n/* harmony export */   useTransition: () => (/* binding */ useTransition)\n/* harmony export */ });\n/* harmony import */ var _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-spring/shared */ \"(ssr)/./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_spring_animated__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-spring/animated */ \"(ssr)/./node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs\");\n/* harmony import */ var _react_spring_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-spring/types */ \"(ssr)/./node_modules/@react-spring/types/dist/react-spring_types.modern.mjs\");\n// src/hooks/useChain.ts\n\n\n// src/helpers.ts\n\nfunction callProp(value, ...args) {\n  return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(value) ? value(...args) : value;\n}\nvar matchProp = (value, key) => value === true || !!(key && value && (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(value) ? value(key) : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(value).includes(key)));\nvar resolveProp = (prop, key) => _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(prop) ? key && prop[key] : prop;\nvar getDefaultProp = (props, key) => props.default === true ? props[key] : props.default ? props.default[key] : void 0;\nvar noopTransform = (value) => value;\nvar getDefaultProps = (props, transform = noopTransform) => {\n  let keys = DEFAULT_PROPS;\n  if (props.default && props.default !== true) {\n    props = props.default;\n    keys = Object.keys(props);\n  }\n  const defaults2 = {};\n  for (const key of keys) {\n    const value = transform(props[key], key);\n    if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(value)) {\n      defaults2[key] = value;\n    }\n  }\n  return defaults2;\n};\nvar DEFAULT_PROPS = [\n  \"config\",\n  \"onProps\",\n  \"onStart\",\n  \"onChange\",\n  \"onPause\",\n  \"onResume\",\n  \"onRest\"\n];\nvar RESERVED_PROPS = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n  // Transition props\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n  // Internal props\n  keys: 1,\n  callId: 1,\n  parentId: 1\n};\nfunction getForwardProps(props) {\n  const forward = {};\n  let count = 0;\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value;\n      count++;\n    }\n  });\n  if (count) {\n    return forward;\n  }\n}\nfunction inferTo(props) {\n  const to2 = getForwardProps(props);\n  if (to2) {\n    const out = { to: to2 };\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(props, (val, key) => key in to2 || (out[key] = val));\n    return out;\n  }\n  return { ...props };\n}\nfunction computeGoal(value) {\n  value = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(value);\n  return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(value) ? value.map(computeGoal) : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isAnimatedString)(value) ? _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.createStringInterpolator({\n    range: [0, 1],\n    output: [value, value]\n  })(1) : value;\n}\nfunction hasProps(props) {\n  for (const _ in props) return true;\n  return false;\n}\nfunction isAsyncTo(to2) {\n  return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(to2) || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(to2) && _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(to2[0]);\n}\nfunction detachRefs(ctrl, ref) {\n  ctrl.ref?.delete(ctrl);\n  ref?.delete(ctrl);\n}\nfunction replaceRef(ctrl, ref) {\n  if (ref && ctrl.ref !== ref) {\n    ctrl.ref?.delete(ctrl);\n    ref.add(ctrl);\n    ctrl.ref = ref;\n  }\n}\n\n// src/hooks/useChain.ts\nfunction useChain(refs, timeSteps, timeFrame = 1e3) {\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    if (timeSteps) {\n      let prevDelay = 0;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(refs, (ref, i) => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i];\n          if (isNaN(delay)) delay = prevDelay;\n          else prevDelay = delay;\n          (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(controllers, (ctrl) => {\n            (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(ctrl.queue, (props) => {\n              const memoizedDelayProp = props.delay;\n              props.delay = (key) => delay + callProp(memoizedDelayProp || 0, key);\n            });\n          });\n          ref.start();\n        }\n      });\n    } else {\n      let p = Promise.resolve();\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(refs, (ref) => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          const queues = controllers.map((ctrl) => {\n            const q = ctrl.queue;\n            ctrl.queue = [];\n            return q;\n          });\n          p = p.then(() => {\n            (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(\n              controllers,\n              (ctrl, i) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(queues[i] || [], (update2) => ctrl.queue.push(update2))\n            );\n            return Promise.all(ref.start());\n          });\n        }\n      });\n    }\n  });\n}\n\n// src/hooks/useSpring.ts\n\n\n// src/hooks/useSprings.ts\n\n\n\n// src/SpringValue.ts\n\n\n\n// src/AnimationConfig.ts\n\n\n// src/constants.ts\nvar config = {\n  default: { tension: 170, friction: 26 },\n  gentle: { tension: 120, friction: 14 },\n  wobbly: { tension: 180, friction: 12 },\n  stiff: { tension: 210, friction: 20 },\n  slow: { tension: 280, friction: 60 },\n  molasses: { tension: 280, friction: 120 }\n};\n\n// src/AnimationConfig.ts\nvar defaults = {\n  ...config.default,\n  mass: 1,\n  damping: 1,\n  easing: _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.easings.linear,\n  clamp: false\n};\nvar AnimationConfig = class {\n  constructor() {\n    /**\n     * The initial velocity of one or more values.\n     *\n     * @default 0\n     */\n    this.velocity = 0;\n    Object.assign(this, defaults);\n  }\n};\nfunction mergeConfig(config2, newConfig, defaultConfig) {\n  if (defaultConfig) {\n    defaultConfig = { ...defaultConfig };\n    sanitizeConfig(defaultConfig, newConfig);\n    newConfig = { ...defaultConfig, ...newConfig };\n  }\n  sanitizeConfig(config2, newConfig);\n  Object.assign(config2, newConfig);\n  for (const key in defaults) {\n    if (config2[key] == null) {\n      config2[key] = defaults[key];\n    }\n  }\n  let { frequency, damping } = config2;\n  const { mass } = config2;\n  if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01;\n    if (damping < 0) damping = 0;\n    config2.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;\n    config2.friction = 4 * Math.PI * damping * mass / frequency;\n  }\n  return config2;\n}\nfunction sanitizeConfig(config2, props) {\n  if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.decay)) {\n    config2.duration = void 0;\n  } else {\n    const isTensionConfig = !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.tension) || !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.friction);\n    if (isTensionConfig || !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.frequency) || !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.damping) || !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.mass)) {\n      config2.duration = void 0;\n      config2.decay = void 0;\n    }\n    if (isTensionConfig) {\n      config2.frequency = void 0;\n    }\n  }\n}\n\n// src/Animation.ts\nvar emptyArray = [];\nvar Animation = class {\n  constructor() {\n    this.changed = false;\n    this.values = emptyArray;\n    this.toValues = null;\n    this.fromValues = emptyArray;\n    this.config = new AnimationConfig();\n    this.immediate = false;\n  }\n};\n\n// src/scheduleProps.ts\n\nfunction scheduleProps(callId, { key, props, defaultProps, state, actions }) {\n  return new Promise((resolve, reject) => {\n    let delay;\n    let timeout;\n    let cancel = matchProp(props.cancel ?? defaultProps?.cancel, key);\n    if (cancel) {\n      onStart();\n    } else {\n      if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.pause)) {\n        state.paused = matchProp(props.pause, key);\n      }\n      let pause = defaultProps?.pause;\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key);\n      }\n      delay = callProp(props.delay || 0, key);\n      if (pause) {\n        state.resumeQueue.add(onResume);\n        actions.pause();\n      } else {\n        actions.resume();\n        onResume();\n      }\n    }\n    function onPause() {\n      state.resumeQueue.add(onResume);\n      state.timeouts.delete(timeout);\n      timeout.cancel();\n      delay = timeout.time - _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.now();\n    }\n    function onResume() {\n      if (delay > 0 && !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.skipAnimation) {\n        state.delayed = true;\n        timeout = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.setTimeout(onStart, delay);\n        state.pauseQueue.add(onPause);\n        state.timeouts.add(timeout);\n      } else {\n        onStart();\n      }\n    }\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false;\n      }\n      state.pauseQueue.delete(onPause);\n      state.timeouts.delete(timeout);\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true;\n      }\n      try {\n        actions.start({ ...props, callId, cancel }, resolve);\n      } catch (err) {\n        reject(err);\n      }\n    }\n  });\n}\n\n// src/runAsync.ts\n\n\n// src/AnimationResult.ts\nvar getCombinedResult = (target, results) => results.length == 1 ? results[0] : results.some((result) => result.cancelled) ? getCancelledResult(target.get()) : results.every((result) => result.noop) ? getNoopResult(target.get()) : getFinishedResult(\n  target.get(),\n  results.every((result) => result.finished)\n);\nvar getNoopResult = (value) => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false\n});\nvar getFinishedResult = (value, finished, cancelled = false) => ({\n  value,\n  finished,\n  cancelled\n});\nvar getCancelledResult = (value) => ({\n  value,\n  cancelled: true,\n  finished: false\n});\n\n// src/runAsync.ts\nfunction runAsync(to2, props, state, target) {\n  const { callId, parentId, onRest } = props;\n  const { asyncTo: prevTo, promise: prevPromise } = state;\n  if (!parentId && to2 === prevTo && !props.reset) {\n    return prevPromise;\n  }\n  return state.promise = (async () => {\n    state.asyncId = callId;\n    state.asyncTo = to2;\n    const defaultProps = getDefaultProps(\n      props,\n      (value, key) => (\n        // The `onRest` prop is only called when the `runAsync` promise is resolved.\n        key === \"onRest\" ? void 0 : value\n      )\n    );\n    let preventBail;\n    let bail;\n    const bailPromise = new Promise(\n      (resolve, reject) => (preventBail = resolve, bail = reject)\n    );\n    const bailIfEnded = (bailSignal) => {\n      const bailResult = (\n        // The `cancel` prop or `stop` method was used.\n        callId <= (state.cancelId || 0) && getCancelledResult(target) || // The async `to` prop was replaced.\n        callId !== state.asyncId && getFinishedResult(target, false)\n      );\n      if (bailResult) {\n        bailSignal.result = bailResult;\n        bail(bailSignal);\n        throw bailSignal;\n      }\n    };\n    const animate = (arg1, arg2) => {\n      const bailSignal = new BailSignal();\n      const skipAnimationSignal = new SkipAnimationSignal();\n      return (async () => {\n        if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.skipAnimation) {\n          stopAsync(state);\n          skipAnimationSignal.result = getFinishedResult(target, false);\n          bail(skipAnimationSignal);\n          throw skipAnimationSignal;\n        }\n        bailIfEnded(bailSignal);\n        const props2 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(arg1) ? { ...arg1 } : { ...arg2, to: arg1 };\n        props2.parentId = callId;\n        (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(defaultProps, (value, key) => {\n          if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props2[key])) {\n            props2[key] = value;\n          }\n        });\n        const result2 = await target.start(props2);\n        bailIfEnded(bailSignal);\n        if (state.paused) {\n          await new Promise((resume) => {\n            state.resumeQueue.add(resume);\n          });\n        }\n        return result2;\n      })();\n    };\n    let result;\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.skipAnimation) {\n      stopAsync(state);\n      return getFinishedResult(target, false);\n    }\n    try {\n      let animating;\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(to2)) {\n        animating = (async (queue) => {\n          for (const props2 of queue) {\n            await animate(props2);\n          }\n        })(to2);\n      } else {\n        animating = Promise.resolve(to2(animate, target.stop.bind(target)));\n      }\n      await Promise.all([animating.then(preventBail), bailPromise]);\n      result = getFinishedResult(target.get(), true, false);\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result;\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result;\n      } else {\n        throw err;\n      }\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId;\n        state.asyncTo = parentId ? prevTo : void 0;\n        state.promise = parentId ? prevPromise : void 0;\n      }\n    }\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(onRest)) {\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => {\n        onRest(result, target, target.item);\n      });\n    }\n    return result;\n  })();\n}\nfunction stopAsync(state, cancelId) {\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flush)(state.timeouts, (t) => t.cancel());\n  state.pauseQueue.clear();\n  state.resumeQueue.clear();\n  state.asyncId = state.asyncTo = state.promise = void 0;\n  if (cancelId) state.cancelId = cancelId;\n}\nvar BailSignal = class extends Error {\n  constructor() {\n    super(\n      \"An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.\"\n    );\n  }\n};\nvar SkipAnimationSignal = class extends Error {\n  constructor() {\n    super(\"SkipAnimationSignal\");\n  }\n};\n\n// src/FrameValue.ts\n\n\nvar isFrameValue = (value) => value instanceof FrameValue;\nvar nextId = 1;\nvar FrameValue = class extends _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.FluidValue {\n  constructor() {\n    super(...arguments);\n    this.id = nextId++;\n    this._priority = 0;\n  }\n  get priority() {\n    return this._priority;\n  }\n  set priority(priority) {\n    if (this._priority != priority) {\n      this._priority = priority;\n      this._onPriorityChange(priority);\n    }\n  }\n  /** Get the current value */\n  get() {\n    const node = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n    return node && node.getValue();\n  }\n  /** Create a spring that maps our value to another value */\n  to(...args) {\n    return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.to(this, args);\n  }\n  /** @deprecated Use the `to` method instead. */\n  interpolate(...args) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.deprecateInterpolate)();\n    return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.to(this, args);\n  }\n  toJSON() {\n    return this.get();\n  }\n  observerAdded(count) {\n    if (count == 1) this._attach();\n  }\n  observerRemoved(count) {\n    if (count == 0) this._detach();\n  }\n  /** Called when the first child is added. */\n  _attach() {\n  }\n  /** Called when the last child is removed. */\n  _detach() {\n  }\n  /** Tell our children about our new value */\n  _onChange(value, idle = false) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.callFluidObservers)(this, {\n      type: \"change\",\n      parent: this,\n      value,\n      idle\n    });\n  }\n  /** Tell our children about our new priority */\n  _onPriorityChange(priority) {\n    if (!this.idle) {\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.frameLoop.sort(this);\n    }\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.callFluidObservers)(this, {\n      type: \"priority\",\n      parent: this,\n      priority\n    });\n  }\n};\n\n// src/SpringPhase.ts\nvar $P = Symbol.for(\"SpringPhase\");\nvar HAS_ANIMATED = 1;\nvar IS_ANIMATING = 2;\nvar IS_PAUSED = 4;\nvar hasAnimated = (target) => (target[$P] & HAS_ANIMATED) > 0;\nvar isAnimating = (target) => (target[$P] & IS_ANIMATING) > 0;\nvar isPaused = (target) => (target[$P] & IS_PAUSED) > 0;\nvar setActiveBit = (target, active) => active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;\nvar setPausedBit = (target, paused) => paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;\n\n// src/SpringValue.ts\nvar SpringValue = class extends FrameValue {\n  constructor(arg1, arg2) {\n    super();\n    /** The animation state */\n    this.animation = new Animation();\n    /** Some props have customizable default values */\n    this.defaultProps = {};\n    /** The state for `runAsync` calls */\n    this._state = {\n      paused: false,\n      delayed: false,\n      pauseQueue: /* @__PURE__ */ new Set(),\n      resumeQueue: /* @__PURE__ */ new Set(),\n      timeouts: /* @__PURE__ */ new Set()\n    };\n    /** The promise resolvers of pending `start` calls */\n    this._pendingCalls = /* @__PURE__ */ new Set();\n    /** The counter for tracking `scheduleProps` calls */\n    this._lastCallId = 0;\n    /** The last `scheduleProps` call that changed the `to` prop */\n    this._lastToId = 0;\n    this._memoizedDuration = 0;\n    if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(arg1) || !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(arg2)) {\n      const props = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(arg1) ? { ...arg1 } : { ...arg2, from: arg1 };\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.default)) {\n        props.default = true;\n      }\n      this.start(props);\n    }\n  }\n  /** Equals true when not advancing on each frame. */\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);\n  }\n  get goal() {\n    return (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(this.animation.to);\n  }\n  get velocity() {\n    const node = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n    return node instanceof _react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.AnimatedValue ? node.lastVelocity || 0 : node.getPayload().map((node2) => node2.lastVelocity || 0);\n  }\n  /**\n   * When true, this value has been animated at least once.\n   */\n  get hasAnimated() {\n    return hasAnimated(this);\n  }\n  /**\n   * When true, this value has an unfinished animation,\n   * which is either active or paused.\n   */\n  get isAnimating() {\n    return isAnimating(this);\n  }\n  /**\n   * When true, all current and future animations are paused.\n   */\n  get isPaused() {\n    return isPaused(this);\n  }\n  /**\n   *\n   *\n   */\n  get isDelayed() {\n    return this._state.delayed;\n  }\n  /** Advance the current animation by a number of milliseconds */\n  advance(dt) {\n    let idle = true;\n    let changed = false;\n    const anim = this.animation;\n    let { toValues } = anim;\n    const { config: config2 } = anim;\n    const payload = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getPayload)(anim.to);\n    if (!payload && (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(anim.to)) {\n      toValues = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(anim.to));\n    }\n    anim.values.forEach((node2, i) => {\n      if (node2.done) return;\n      const to2 = (\n        // Animated strings always go from 0 to 1.\n        node2.constructor == _react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.AnimatedString ? 1 : payload ? payload[i].lastPosition : toValues[i]\n      );\n      let finished = anim.immediate;\n      let position = to2;\n      if (!finished) {\n        position = node2.lastPosition;\n        if (config2.tension <= 0) {\n          node2.done = true;\n          return;\n        }\n        let elapsed = node2.elapsedTime += dt;\n        const from = anim.fromValues[i];\n        const v0 = node2.v0 != null ? node2.v0 : node2.v0 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(config2.velocity) ? config2.velocity[i] : config2.velocity;\n        let velocity;\n        const precision = config2.precision || (from == to2 ? 5e-3 : Math.min(1, Math.abs(to2 - from) * 1e-3));\n        if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(config2.duration)) {\n          let p = 1;\n          if (config2.duration > 0) {\n            if (this._memoizedDuration !== config2.duration) {\n              this._memoizedDuration = config2.duration;\n              if (node2.durationProgress > 0) {\n                node2.elapsedTime = config2.duration * node2.durationProgress;\n                elapsed = node2.elapsedTime += dt;\n              }\n            }\n            p = (config2.progress || 0) + elapsed / this._memoizedDuration;\n            p = p > 1 ? 1 : p < 0 ? 0 : p;\n            node2.durationProgress = p;\n          }\n          position = from + config2.easing(p) * (to2 - from);\n          velocity = (position - node2.lastPosition) / dt;\n          finished = p == 1;\n        } else if (config2.decay) {\n          const decay = config2.decay === true ? 0.998 : config2.decay;\n          const e = Math.exp(-(1 - decay) * elapsed);\n          position = from + v0 / (1 - decay) * (1 - e);\n          finished = Math.abs(node2.lastPosition - position) <= precision;\n          velocity = v0 * e;\n        } else {\n          velocity = node2.lastVelocity == null ? v0 : node2.lastVelocity;\n          const restVelocity = config2.restVelocity || precision / 10;\n          const bounceFactor = config2.clamp ? 0 : config2.bounce;\n          const canBounce = !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(bounceFactor);\n          const isGrowing = from == to2 ? node2.v0 > 0 : from < to2;\n          let isMoving;\n          let isBouncing = false;\n          const step = 1;\n          const numSteps = Math.ceil(dt / step);\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity;\n            if (!isMoving) {\n              finished = Math.abs(to2 - position) <= precision;\n              if (finished) {\n                break;\n              }\n            }\n            if (canBounce) {\n              isBouncing = position == to2 || position > to2 == isGrowing;\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor;\n                position = to2;\n              }\n            }\n            const springForce = -config2.tension * 1e-6 * (position - to2);\n            const dampingForce = -config2.friction * 1e-3 * velocity;\n            const acceleration = (springForce + dampingForce) / config2.mass;\n            velocity = velocity + acceleration * step;\n            position = position + velocity * step;\n          }\n        }\n        node2.lastVelocity = velocity;\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this);\n          finished = true;\n        }\n      }\n      if (payload && !payload[i].done) {\n        finished = false;\n      }\n      if (finished) {\n        node2.done = true;\n      } else {\n        idle = false;\n      }\n      if (node2.setValue(position, config2.round)) {\n        changed = true;\n      }\n    });\n    const node = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n    const currVal = node.getValue();\n    if (idle) {\n      const finalVal = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(anim.to);\n      if ((currVal !== finalVal || changed) && !config2.decay) {\n        node.setValue(finalVal);\n        this._onChange(finalVal);\n      } else if (changed && config2.decay) {\n        this._onChange(currVal);\n      }\n      this._stop();\n    } else if (changed) {\n      this._onChange(currVal);\n    }\n  }\n  /** Set the current value, while stopping the current animation */\n  set(value) {\n    _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => {\n      this._stop();\n      this._focus(value);\n      this._set(value);\n    });\n    return this;\n  }\n  /**\n   * Freeze the active animation in time, as well as any updates merged\n   * before `resume` is called.\n   */\n  pause() {\n    this._update({ pause: true });\n  }\n  /** Resume the animation if paused. */\n  resume() {\n    this._update({ pause: false });\n  }\n  /** Skip to the end of the current animation. */\n  finish() {\n    if (isAnimating(this)) {\n      const { to: to2, config: config2 } = this.animation;\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => {\n        this._onStart();\n        if (!config2.decay) {\n          this._set(to2, false);\n        }\n        this._stop();\n      });\n    }\n    return this;\n  }\n  /** Push props into the pending queue. */\n  update(props) {\n    const queue = this.queue || (this.queue = []);\n    queue.push(props);\n    return this;\n  }\n  start(to2, arg2) {\n    let queue;\n    if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(to2)) {\n      queue = [_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(to2) ? to2 : { ...arg2, to: to2 }];\n    } else {\n      queue = this.queue || [];\n      this.queue = [];\n    }\n    return Promise.all(\n      queue.map((props) => {\n        const up = this._update(props);\n        return up;\n      })\n    ).then((results) => getCombinedResult(this, results));\n  }\n  /**\n   * Stop the current animation, and cancel any delayed updates.\n   *\n   * Pass `true` to call `onRest` with `cancelled: true`.\n   */\n  stop(cancel) {\n    const { to: to2 } = this.animation;\n    this._focus(this.get());\n    stopAsync(this._state, cancel && this._lastCallId);\n    _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => this._stop(to2, cancel));\n    return this;\n  }\n  /** Restart the animation. */\n  reset() {\n    this._update({ reset: true });\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._start();\n    } else if (event.type == \"priority\") {\n      this.priority = event.priority + 1;\n    }\n  }\n  /**\n   * Parse the `to` and `from` range from the given `props` object.\n   *\n   * This also ensures the initial value is available to animated components\n   * during the render phase.\n   */\n  _prepareNode(props) {\n    const key = this.key || \"\";\n    let { to: to2, from } = props;\n    to2 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(to2) ? to2[key] : to2;\n    if (to2 == null || isAsyncTo(to2)) {\n      to2 = void 0;\n    }\n    from = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(from) ? from[key] : from;\n    if (from == null) {\n      from = void 0;\n    }\n    const range = { to: to2, from };\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to2, from] = [from, to2];\n      from = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(from);\n      if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(from)) {\n        this._set(from);\n      } else if (!(0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this)) {\n        this._set(to2);\n      }\n    }\n    return range;\n  }\n  /** Every update is processed by this method before merging. */\n  _update({ ...props }, isLoop) {\n    const { key, defaultProps } = this;\n    if (props.default)\n      Object.assign(\n        defaultProps,\n        getDefaultProps(\n          props,\n          (value, prop) => /^on/.test(prop) ? resolveProp(value, key) : value\n        )\n      );\n    mergeActiveFn(this, props, \"onProps\");\n    sendEvent(this, \"onProps\", props, this);\n    const range = this._prepareNode(props);\n    if (Object.isFrozen(this)) {\n      throw Error(\n        \"Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?\"\n      );\n    }\n    const state = this._state;\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true);\n            (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flushCalls)(state.pauseQueue);\n            sendEvent(\n              this,\n              \"onPause\",\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            );\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false);\n            if (isAnimating(this)) {\n              this._resume();\n            }\n            (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flushCalls)(state.resumeQueue);\n            sendEvent(\n              this,\n              \"onResume\",\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            );\n          }\n        },\n        start: this._merge.bind(this, range)\n      }\n    }).then((result) => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props);\n        if (nextProps) {\n          return this._update(nextProps, true);\n        }\n      }\n      return result;\n    });\n  }\n  /** Merge props into the current animation */\n  _merge(range, props, resolve) {\n    if (props.cancel) {\n      this.stop(true);\n      return resolve(getCancelledResult(this));\n    }\n    const hasToProp = !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(range.to);\n    const hasFromProp = !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(range.from);\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId;\n      } else {\n        return resolve(getCancelledResult(this));\n      }\n    }\n    const { key, defaultProps, animation: anim } = this;\n    const { to: prevTo, from: prevFrom } = anim;\n    let { to: to2 = prevTo, from = prevFrom } = range;\n    if (hasFromProp && !hasToProp && (!props.default || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(to2))) {\n      to2 = from;\n    }\n    if (props.reverse) [to2, from] = [from, to2];\n    const hasFromChanged = !(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(from, prevFrom);\n    if (hasFromChanged) {\n      anim.from = from;\n    }\n    from = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(from);\n    const hasToChanged = !(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(to2, prevTo);\n    if (hasToChanged) {\n      this._focus(to2);\n    }\n    const hasAsyncTo = isAsyncTo(props.to);\n    const { config: config2 } = anim;\n    const { decay, velocity } = config2;\n    if (hasToProp || hasFromProp) {\n      config2.velocity = 0;\n    }\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(\n        config2,\n        callProp(props.config, key),\n        // Avoid calling the same \"config\" prop twice.\n        props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0\n      );\n    }\n    let node = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n    if (!node || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(to2)) {\n      return resolve(getFinishedResult(this, true));\n    }\n    const reset = (\n      // When `reset` is undefined, the `from` prop implies `reset: true`,\n      // except for declarative updates. When `reset` is defined, there\n      // must exist a value to animate from.\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props.reset) ? hasFromProp && !props.default : !_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(from) && matchProp(props.reset, key)\n    );\n    const value = reset ? from : this.get();\n    const goal = computeGoal(to2);\n    const isAnimatable = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.num(goal) || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(goal) || (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isAnimatedString)(goal);\n    const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));\n    if (hasToChanged) {\n      const nodeType = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimatedType)(to2);\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal);\n        } else\n          throw Error(\n            `Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`\n          );\n      }\n    }\n    const goalType = node.constructor;\n    let started = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(to2);\n    let finished = false;\n    if (!started) {\n      const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;\n      if (hasToChanged || hasValueChanged) {\n        finished = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(computeGoal(value), goal);\n        started = !finished;\n      }\n      if (!(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(anim.immediate, immediate) && !immediate || !(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(config2.decay, decay) || !(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(config2.velocity, velocity)) {\n        started = true;\n      }\n    }\n    if (finished && isAnimating(this)) {\n      if (anim.changed && !reset) {\n        started = true;\n      } else if (!started) {\n        this._stop(prevTo);\n      }\n    }\n    if (!hasAsyncTo) {\n      if (started || (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(prevTo)) {\n        anim.values = node.getPayload();\n        anim.toValues = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(to2) ? null : goalType == _react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.AnimatedString ? [1] : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(goal);\n      }\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate;\n        if (!immediate && !reset) {\n          this._set(prevTo);\n        }\n      }\n      if (started) {\n        const { onRest } = anim;\n        (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(ACTIVE_EVENTS, (type) => mergeActiveFn(this, props, type));\n        const result = getFinishedResult(this, checkFinished(this, prevTo));\n        (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flushCalls)(this._pendingCalls, result);\n        this._pendingCalls.add(resolve);\n        if (anim.changed)\n          _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => {\n            anim.changed = !reset;\n            onRest?.(result, this);\n            if (reset) {\n              callProp(defaultProps.onRest, result);\n            } else {\n              anim.onStart?.(result, this);\n            }\n          });\n      }\n    }\n    if (reset) {\n      this._set(value);\n    }\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this));\n    } else if (started) {\n      this._start();\n    } else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve);\n    } else {\n      resolve(getNoopResult(value));\n    }\n  }\n  /** Update the `animation.to` value, which might be a `FluidValue` */\n  _focus(value) {\n    const anim = this.animation;\n    if (value !== anim.to) {\n      if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidObservers)(this)) {\n        this._detach();\n      }\n      anim.to = value;\n      if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidObservers)(this)) {\n        this._attach();\n      }\n    }\n  }\n  _attach() {\n    let priority = 0;\n    const { to: to2 } = this.animation;\n    if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(to2)) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.addFluidObserver)(to2, this);\n      if (isFrameValue(to2)) {\n        priority = to2.priority + 1;\n      }\n    }\n    this.priority = priority;\n  }\n  _detach() {\n    const { to: to2 } = this.animation;\n    if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(to2)) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.removeFluidObserver)(to2, this);\n    }\n  }\n  /**\n   * Update the current value from outside the frameloop,\n   * and return the `Animated` node.\n   */\n  _set(arg, idle = true) {\n    const value = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(arg);\n    if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(value)) {\n      const oldNode = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n      if (!oldNode || !(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(value, oldNode.getValue())) {\n        const nodeType = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimatedType)(value);\n        if (!oldNode || oldNode.constructor != nodeType) {\n          (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.setAnimated)(this, nodeType.create(value));\n        } else {\n          oldNode.setValue(value);\n        }\n        if (oldNode) {\n          _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => {\n            this._onChange(value, idle);\n          });\n        }\n      }\n    }\n    return (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this);\n  }\n  _onStart() {\n    const anim = this.animation;\n    if (!anim.changed) {\n      anim.changed = true;\n      sendEvent(\n        this,\n        \"onStart\",\n        getFinishedResult(this, checkFinished(this, anim.to)),\n        this\n      );\n    }\n  }\n  _onChange(value, idle) {\n    if (!idle) {\n      this._onStart();\n      callProp(this.animation.onChange, value, this);\n    }\n    callProp(this.defaultProps.onChange, value, this);\n    super._onChange(value, idle);\n  }\n  // This method resets the animation state (even if already animating) to\n  // ensure the latest from/to range is used, and it also ensures this spring\n  // is added to the frameloop.\n  _start() {\n    const anim = this.animation;\n    (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this).reset((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(anim.to));\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map((node) => node.lastPosition);\n    }\n    if (!isAnimating(this)) {\n      setActiveBit(this, true);\n      if (!isPaused(this)) {\n        this._resume();\n      }\n    }\n  }\n  _resume() {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.skipAnimation) {\n      this.finish();\n    } else {\n      _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.frameLoop.start(this);\n    }\n  }\n  /**\n   * Exit the frameloop and notify `onRest` listeners.\n   *\n   * Always wrap `_stop` calls with `batchedUpdates`.\n   */\n  _stop(goal, cancel) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false);\n      const anim = this.animation;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(anim.values, (node) => {\n        node.done = true;\n      });\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = void 0;\n      }\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.callFluidObservers)(this, {\n        type: \"idle\",\n        parent: this\n      });\n      const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to));\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flushCalls)(this._pendingCalls, result);\n      if (anim.changed) {\n        anim.changed = false;\n        sendEvent(this, \"onRest\", result, this);\n      }\n    }\n  }\n};\nfunction checkFinished(target, to2) {\n  const goal = computeGoal(to2);\n  const value = computeGoal(target.get());\n  return (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(value, goal);\n}\nfunction createLoopUpdate(props, loop = props.loop, to2 = props.to) {\n  const loopRet = callProp(loop);\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet);\n    const reverse = (overrides || props).reverse;\n    const reset = !overrides || overrides.reset;\n    return createUpdate({\n      ...props,\n      loop,\n      // Avoid updating default props when looping.\n      default: false,\n      // Never loop the `pause` prop.\n      pause: void 0,\n      // For the \"reverse\" prop to loop as expected, the \"to\" prop\n      // must be undefined. The \"reverse\" prop is ignored when the\n      // \"to\" prop is an array or function.\n      to: !reverse || isAsyncTo(to2) ? to2 : void 0,\n      // Ignore the \"from\" prop except on reset.\n      from: reset ? props.from : void 0,\n      reset,\n      // The \"loop\" prop can return a \"useSpring\" props object to\n      // override any of the original props.\n      ...overrides\n    });\n  }\n}\nfunction createUpdate(props) {\n  const { to: to2, from } = props = inferTo(props);\n  const keys = /* @__PURE__ */ new Set();\n  if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(to2)) findDefined(to2, keys);\n  if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(from)) findDefined(from, keys);\n  props.keys = keys.size ? Array.from(keys) : null;\n  return props;\n}\nfunction declareUpdate(props) {\n  const update2 = createUpdate(props);\n  if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(update2.default)) {\n    update2.default = getDefaultProps(update2);\n  }\n  return update2;\n}\nfunction findDefined(values, keys) {\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(values, (value, key) => value != null && keys.add(key));\n}\nvar ACTIVE_EVENTS = [\n  \"onStart\",\n  \"onRest\",\n  \"onChange\",\n  \"onPause\",\n  \"onResume\"\n];\nfunction mergeActiveFn(target, props, type) {\n  target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : void 0;\n}\nfunction sendEvent(target, type, ...args) {\n  target.animation[type]?.(...args);\n  target.defaultProps[type]?.(...args);\n}\n\n// src/Controller.ts\n\nvar BATCHED_EVENTS = [\"onStart\", \"onChange\", \"onRest\"];\nvar nextId2 = 1;\nvar Controller = class {\n  constructor(props, flush3) {\n    this.id = nextId2++;\n    /** The animated values */\n    this.springs = {};\n    /** The queue of props passed to the `update` method. */\n    this.queue = [];\n    /** The counter for tracking `scheduleProps` calls */\n    this._lastAsyncId = 0;\n    /** The values currently being animated */\n    this._active = /* @__PURE__ */ new Set();\n    /** The values that changed recently */\n    this._changed = /* @__PURE__ */ new Set();\n    /** Equals false when `onStart` listeners can be called */\n    this._started = false;\n    /** State used by the `runAsync` function */\n    this._state = {\n      paused: false,\n      pauseQueue: /* @__PURE__ */ new Set(),\n      resumeQueue: /* @__PURE__ */ new Set(),\n      timeouts: /* @__PURE__ */ new Set()\n    };\n    /** The event queues that are flushed once per frame maximum */\n    this._events = {\n      onStart: /* @__PURE__ */ new Map(),\n      onChange: /* @__PURE__ */ new Map(),\n      onRest: /* @__PURE__ */ new Map()\n    };\n    this._onFrame = this._onFrame.bind(this);\n    if (flush3) {\n      this._flush = flush3;\n    }\n    if (props) {\n      this.start({ default: true, ...props });\n    }\n  }\n  /**\n   * Equals `true` when no spring values are in the frameloop, and\n   * no async animation is currently active.\n   */\n  get idle() {\n    return !this._state.asyncTo && Object.values(this.springs).every((spring) => {\n      return spring.idle && !spring.isDelayed && !spring.isPaused;\n    });\n  }\n  get item() {\n    return this._item;\n  }\n  set item(item) {\n    this._item = item;\n  }\n  /** Get the current values of our springs */\n  get() {\n    const values = {};\n    this.each((spring, key) => values[key] = spring.get());\n    return values;\n  }\n  /** Set the current values without animating. */\n  set(values) {\n    for (const key in values) {\n      const value = values[key];\n      if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(value)) {\n        this.springs[key].set(value);\n      }\n    }\n  }\n  /** Push an update onto the queue of each value. */\n  update(props) {\n    if (props) {\n      this.queue.push(createUpdate(props));\n    }\n    return this;\n  }\n  /**\n   * Start the queued animations for every spring, and resolve the returned\n   * promise once all queued animations have finished or been cancelled.\n   *\n   * When you pass a queue (instead of nothing), that queue is used instead of\n   * the queued animations added with the `update` method, which are left alone.\n   */\n  start(props) {\n    let { queue } = this;\n    if (props) {\n      queue = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(props).map(createUpdate);\n    } else {\n      this.queue = [];\n    }\n    if (this._flush) {\n      return this._flush(this, queue);\n    }\n    prepareKeys(this, queue);\n    return flushUpdateQueue(this, queue);\n  }\n  /** @internal */\n  stop(arg, keys) {\n    if (arg !== !!arg) {\n      keys = arg;\n    }\n    if (keys) {\n      const springs = this.springs;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(keys), (key) => springs[key].stop(!!arg));\n    } else {\n      stopAsync(this._state, this._lastAsyncId);\n      this.each((spring) => spring.stop(!!arg));\n    }\n    return this;\n  }\n  /** Freeze the active animation in time */\n  pause(keys) {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(keys)) {\n      this.start({ pause: true });\n    } else {\n      const springs = this.springs;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(keys), (key) => springs[key].pause());\n    }\n    return this;\n  }\n  /** Resume the animation if paused. */\n  resume(keys) {\n    if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(keys)) {\n      this.start({ pause: false });\n    } else {\n      const springs = this.springs;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(keys), (key) => springs[key].resume());\n    }\n    return this;\n  }\n  /** Call a function once per spring value */\n  each(iterator) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(this.springs, iterator);\n  }\n  /** @internal Called at the end of every animation frame */\n  _onFrame() {\n    const { onStart, onChange, onRest } = this._events;\n    const active = this._active.size > 0;\n    const changed = this._changed.size > 0;\n    if (active && !this._started || changed && !this._started) {\n      this._started = true;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flush)(onStart, ([onStart2, result]) => {\n        result.value = this.get();\n        onStart2(result, this, this._item);\n      });\n    }\n    const idle = !active && this._started;\n    const values = changed || idle && onRest.size ? this.get() : null;\n    if (changed && onChange.size) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flush)(onChange, ([onChange2, result]) => {\n        result.value = values;\n        onChange2(result, this, this._item);\n      });\n    }\n    if (idle) {\n      this._started = false;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flush)(onRest, ([onRest2, result]) => {\n        result.value = values;\n        onRest2(result, this, this._item);\n      });\n    }\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._changed.add(event.parent);\n      if (!event.idle) {\n        this._active.add(event.parent);\n      }\n    } else if (event.type == \"idle\") {\n      this._active.delete(event.parent);\n    } else return;\n    _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.onFrame(this._onFrame);\n  }\n};\nfunction flushUpdateQueue(ctrl, queue) {\n  return Promise.all(queue.map((props) => flushUpdate(ctrl, props))).then(\n    (results) => getCombinedResult(ctrl, results)\n  );\n}\nasync function flushUpdate(ctrl, props, isLoop) {\n  const { keys, to: to2, from, loop, onRest, onResolve } = props;\n  const defaults2 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(props.default) && props.default;\n  if (loop) {\n    props.loop = false;\n  }\n  if (to2 === false) props.to = null;\n  if (from === false) props.from = null;\n  const asyncTo = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(to2) || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(to2) ? to2 : void 0;\n  if (asyncTo) {\n    props.to = void 0;\n    props.onRest = void 0;\n    if (defaults2) {\n      defaults2.onRest = void 0;\n    }\n  } else {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(BATCHED_EVENTS, (key) => {\n      const handler = props[key];\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(handler)) {\n        const queue = ctrl[\"_events\"][key];\n        props[key] = ({ finished, cancelled }) => {\n          const result2 = queue.get(handler);\n          if (result2) {\n            if (!finished) result2.finished = false;\n            if (cancelled) result2.cancelled = true;\n          } else {\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false\n            });\n          }\n        };\n        if (defaults2) {\n          defaults2[key] = props[key];\n        }\n      }\n    });\n  }\n  const state = ctrl[\"_state\"];\n  if (props.pause === !state.paused) {\n    state.paused = props.pause;\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.flushCalls)(props.pause ? state.pauseQueue : state.resumeQueue);\n  } else if (state.paused) {\n    props.pause = true;\n  }\n  const promises = (keys || Object.keys(ctrl.springs)).map(\n    (key) => ctrl.springs[key].start(props)\n  );\n  const cancel = props.cancel === true || getDefaultProp(props, \"cancel\") === true;\n  if (asyncTo || cancel && state.asyncId) {\n    promises.push(\n      scheduleProps(++ctrl[\"_lastAsyncId\"], {\n        props,\n        state,\n        actions: {\n          pause: _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.noop,\n          resume: _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.noop,\n          start(props2, resolve) {\n            if (cancel) {\n              stopAsync(state, ctrl[\"_lastAsyncId\"]);\n              resolve(getCancelledResult(ctrl));\n            } else {\n              props2.onRest = onRest;\n              resolve(\n                runAsync(\n                  asyncTo,\n                  props2,\n                  state,\n                  ctrl\n                )\n              );\n            }\n          }\n        }\n      })\n    );\n  }\n  if (state.paused) {\n    await new Promise((resume) => {\n      state.resumeQueue.add(resume);\n    });\n  }\n  const result = getCombinedResult(ctrl, await Promise.all(promises));\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to2);\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps]);\n      return flushUpdate(ctrl, nextProps, true);\n    }\n  }\n  if (onResolve) {\n    _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item));\n  }\n  return result;\n}\nfunction getSprings(ctrl, props) {\n  const springs = { ...ctrl.springs };\n  if (props) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(props), (props2) => {\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props2.keys)) {\n        props2 = createUpdate(props2);\n      }\n      if (!_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(props2.to)) {\n        props2 = { ...props2, to: void 0 };\n      }\n      prepareSprings(springs, props2, (key) => {\n        return createSpring(key);\n      });\n    });\n  }\n  setSprings(ctrl, springs);\n  return springs;\n}\nfunction setSprings(ctrl, springs) {\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.eachProp)(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.addFluidObserver)(spring, ctrl);\n    }\n  });\n}\nfunction createSpring(key, observer) {\n  const spring = new SpringValue();\n  spring.key = key;\n  if (observer) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.addFluidObserver)(spring, observer);\n  }\n  return spring;\n}\nfunction prepareSprings(springs, props, create) {\n  if (props.keys) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(props.keys, (key) => {\n      const spring = springs[key] || (springs[key] = create(key));\n      spring[\"_prepareNode\"](props);\n    });\n  }\n}\nfunction prepareKeys(ctrl, queue) {\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(queue, (props) => {\n    prepareSprings(ctrl.springs, props, (key) => {\n      return createSpring(key, ctrl);\n    });\n  });\n}\n\n// src/SpringContext.tsx\n\n\nvar SpringContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext({\n  pause: false,\n  immediate: false\n});\n\n// src/SpringRef.ts\n\nvar SpringRef = () => {\n  const current = [];\n  const SpringRef2 = function(props) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.deprecateDirectCall)();\n    const results = [];\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl, i) => {\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update2 = _getProps(props, ctrl, i);\n        if (update2) {\n          results.push(ctrl.start(update2));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef2.current = current;\n  SpringRef2.add = function(ctrl) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl);\n    }\n  };\n  SpringRef2.delete = function(ctrl) {\n    const i = current.indexOf(ctrl);\n    if (~i) current.splice(i, 1);\n  };\n  SpringRef2.pause = function() {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl) => ctrl.pause(...arguments));\n    return this;\n  };\n  SpringRef2.resume = function() {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl) => ctrl.resume(...arguments));\n    return this;\n  };\n  SpringRef2.set = function(values) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl, i) => {\n      const update2 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(values) ? values(i, ctrl) : values;\n      if (update2) {\n        ctrl.set(update2);\n      }\n    });\n  };\n  SpringRef2.start = function(props) {\n    const results = [];\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl, i) => {\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update2 = this._getProps(props, ctrl, i);\n        if (update2) {\n          results.push(ctrl.start(update2));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef2.stop = function() {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl) => ctrl.stop(...arguments));\n    return this;\n  };\n  SpringRef2.update = function(props) {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)));\n    return this;\n  };\n  const _getProps = function(arg, ctrl, index) {\n    return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(arg) ? arg(index, ctrl) : arg;\n  };\n  SpringRef2._getProps = _getProps;\n  return SpringRef2;\n};\n\n// src/hooks/useSprings.ts\nfunction useSprings(length, props, deps) {\n  const propsFn = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(props) && props;\n  if (propsFn && !deps) deps = [];\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(\n    () => propsFn || arguments.length == 3 ? SpringRef() : void 0,\n    []\n  );\n  const layoutId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n  const forceUpdate = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useForceUpdate)();\n  const state = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(\n    () => ({\n      ctrls: [],\n      queue: [],\n      flush(ctrl, updates2) {\n        const springs2 = getSprings(ctrl, updates2);\n        const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs2).some((key) => !ctrl.springs[key]);\n        return canFlushSync ? flushUpdateQueue(ctrl, updates2) : new Promise((resolve) => {\n          setSprings(ctrl, springs2);\n          state.queue.push(() => {\n            resolve(flushUpdateQueue(ctrl, updates2));\n          });\n          forceUpdate();\n        });\n      }\n    }),\n    []\n  );\n  const ctrls = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([...state.ctrls]);\n  const updates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n  const prevLength = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.usePrev)(length) || 0;\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(ctrls.current.slice(length, prevLength), (ctrl) => {\n      detachRefs(ctrl, ref);\n      ctrl.stop(true);\n    });\n    ctrls.current.length = length;\n    declareUpdates(prevLength, length);\n  }, [length]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n    declareUpdates(0, Math.min(prevLength, length));\n  }, deps);\n  function declareUpdates(startIndex, endIndex) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl = ctrls.current[i] || (ctrls.current[i] = new Controller(null, state.flush));\n      const update2 = propsFn ? propsFn(i, ctrl) : props[i];\n      if (update2) {\n        updates.current[i] = declareUpdate(update2);\n      }\n    }\n  }\n  const springs = ctrls.current.map(\n    (ctrl, i) => getSprings(ctrl, updates.current[i])\n  );\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SpringContext);\n  const prevContext = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.usePrev)(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    layoutId.current++;\n    state.ctrls = ctrls.current;\n    const { queue } = state;\n    if (queue.length) {\n      state.queue = [];\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(queue, (cb) => cb());\n    }\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(ctrls.current, (ctrl, i) => {\n      ref?.add(ctrl);\n      if (hasContext) {\n        ctrl.start({ default: context });\n      }\n      const update2 = updates.current[i];\n      if (update2) {\n        replaceRef(ctrl, update2.ref);\n        if (ctrl.ref) {\n          ctrl.queue.push(update2);\n        } else {\n          ctrl.start(update2);\n        }\n      }\n    });\n  });\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useOnce)(() => () => {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(state.ctrls, (ctrl) => ctrl.stop(true));\n  });\n  const values = springs.map((x) => ({ ...x }));\n  return ref ? [values, ref] : values;\n}\n\n// src/hooks/useSpring.ts\nfunction useSpring(props, deps) {\n  const isFn = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(props);\n  const [[values], ref] = useSprings(\n    1,\n    isFn ? props : [props],\n    isFn ? deps || [] : deps\n  );\n  return isFn || arguments.length == 2 ? [values, ref] : values;\n}\n\n// src/hooks/useSpringRef.ts\n\nvar initSpringRef = () => SpringRef();\nvar useSpringRef = () => (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initSpringRef)[0];\n\n// src/hooks/useSpringValue.ts\n\nvar useSpringValue = (initial, props) => {\n  const springValue = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useConstant)(() => new SpringValue(initial, props));\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useOnce)(() => () => {\n    springValue.stop();\n  });\n  return springValue;\n};\n\n// src/hooks/useTrail.ts\n\nfunction useTrail(length, propsArg, deps) {\n  const propsFn = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(propsArg) && propsArg;\n  if (propsFn && !deps) deps = [];\n  let reverse = true;\n  let passedRef = void 0;\n  const result = useSprings(\n    length,\n    (i, ctrl) => {\n      const props = propsFn ? propsFn(i, ctrl) : propsArg;\n      passedRef = props.ref;\n      reverse = reverse && props.reverse;\n      return props;\n    },\n    // Ensure the props function is called when no deps exist.\n    // This works around the 3 argument rule.\n    deps || [{}]\n  );\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)];\n      replaceRef(ctrl, passedRef);\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({ to: parent.springs });\n        }\n        return;\n      }\n      if (parent) {\n        ctrl.start({ to: parent.springs });\n      } else {\n        ctrl.start();\n      }\n    });\n  }, deps);\n  if (propsFn || arguments.length == 3) {\n    const ref = passedRef ?? result[1];\n    ref[\"_getProps\"] = (propsArg2, ctrl, i) => {\n      const props = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(propsArg2) ? propsArg2(i, ctrl) : propsArg2;\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)];\n        if (parent) props.to = parent.springs;\n        return props;\n      }\n    };\n    return result;\n  }\n  return result[0];\n}\n\n// src/hooks/useTransition.tsx\n\n\n\nfunction useTransition(data, props, deps) {\n  const propsFn = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(props) && props;\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig\n  } = propsFn ? propsFn() : props;\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(\n    () => propsFn || arguments.length == 3 ? SpringRef() : void 0,\n    []\n  );\n  const items = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(data);\n  const transitions = [];\n  const usedTransitions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  const prevTransitions = reset ? null : usedTransitions.current;\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    usedTransitions.current = transitions;\n  });\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useOnce)(() => {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(transitions, (t) => {\n      ref?.add(t.ctrl);\n      t.ctrl.ref = ref;\n    });\n    return () => {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(usedTransitions.current, (t) => {\n        if (t.expired) {\n          clearTimeout(t.expirationId);\n        }\n        detachRefs(t.ctrl, ref);\n        t.ctrl.stop(true);\n      });\n    };\n  });\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);\n  const expired = reset && usedTransitions.current || [];\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(\n    () => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(expired, ({ ctrl, item, key }) => {\n      detachRefs(ctrl, ref);\n      callProp(onDestroyed, item, key);\n    })\n  );\n  const reused = [];\n  if (prevTransitions)\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(prevTransitions, (t, i) => {\n      if (t.expired) {\n        clearTimeout(t.expirationId);\n        expired.push(t);\n      } else {\n        i = reused[i] = keys.indexOf(t.key);\n        if (~i) transitions[i] = t;\n      }\n    });\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: \"mount\" /* MOUNT */,\n        ctrl: new Controller()\n      };\n      transitions[i].ctrl.item = item;\n    }\n  });\n  if (reused.length) {\n    let i = -1;\n    const { leave } = propsFn ? propsFn() : props;\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions[prevIndex];\n      if (~keyIndex) {\n        i = transitions.indexOf(t);\n        transitions[i] = { ...t, item: items[keyIndex] };\n      } else if (leave) {\n        transitions.splice(++i, 0, t);\n      }\n    });\n  }\n  if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item));\n  }\n  let delay = -trail;\n  const forceUpdate = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useForceUpdate)();\n  const defaultProps = getDefaultProps(props);\n  const changes = /* @__PURE__ */ new Map();\n  const exitingTransitions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(/* @__PURE__ */ new Map());\n  const forceChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(transitions, (t, i) => {\n    const key = t.key;\n    const prevPhase = t.phase;\n    const p = propsFn ? propsFn() : props;\n    let to2;\n    let phase;\n    const propsDelay = callProp(p.delay || 0, key);\n    if (prevPhase == \"mount\" /* MOUNT */) {\n      to2 = p.enter;\n      phase = \"enter\" /* ENTER */;\n    } else {\n      const isLeave = keys.indexOf(key) < 0;\n      if (prevPhase != \"leave\" /* LEAVE */) {\n        if (isLeave) {\n          to2 = p.leave;\n          phase = \"leave\" /* LEAVE */;\n        } else if (to2 = p.update) {\n          phase = \"update\" /* UPDATE */;\n        } else return;\n      } else if (!isLeave) {\n        to2 = p.enter;\n        phase = \"enter\" /* ENTER */;\n      } else return;\n    }\n    to2 = callProp(to2, t.item, i);\n    to2 = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.obj(to2) ? inferTo(to2) : { to: to2 };\n    if (!to2.config) {\n      const config2 = propsConfig || defaultProps.config;\n      to2.config = callProp(config2, t.item, i, phase);\n    }\n    delay += trail;\n    const payload = {\n      ...defaultProps,\n      // we need to add our props.delay value you here.\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      // This prevents implied resets.\n      reset: false,\n      // Merge any phase-specific props.\n      ...to2\n    };\n    if (phase == \"enter\" /* ENTER */ && _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(payload.from)) {\n      const p2 = propsFn ? propsFn() : props;\n      const from = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(p2.initial) || prevTransitions ? p2.from : p2.initial;\n      payload.from = callProp(from, t.item, i);\n    }\n    const { onResolve } = payload;\n    payload.onResolve = (result) => {\n      callProp(onResolve, result);\n      const transitions2 = usedTransitions.current;\n      const t2 = transitions2.find((t3) => t3.key === key);\n      if (!t2) return;\n      if (result.cancelled && t2.phase != \"update\" /* UPDATE */) {\n        return;\n      }\n      if (t2.ctrl.idle) {\n        const idle = transitions2.every((t3) => t3.ctrl.idle);\n        if (t2.phase == \"leave\" /* LEAVE */) {\n          const expiry = callProp(expires, t2.item);\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry;\n            t2.expired = true;\n            if (!idle && expiryMs > 0) {\n              if (expiryMs <= 2147483647)\n                t2.expirationId = setTimeout(forceUpdate, expiryMs);\n              return;\n            }\n          }\n        }\n        if (idle && transitions2.some((t3) => t3.expired)) {\n          exitingTransitions.current.delete(t2);\n          if (exitBeforeEnter) {\n            forceChange.current = true;\n          }\n          forceUpdate();\n        }\n      }\n    };\n    const springs = getSprings(t.ctrl, payload);\n    if (phase === \"leave\" /* LEAVE */ && exitBeforeEnter) {\n      exitingTransitions.current.set(t, { phase, springs, payload });\n    } else {\n      changes.set(t, { phase, springs, payload });\n    }\n  });\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SpringContext);\n  const prevContext = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.usePrev)(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    if (hasContext) {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(transitions, (t) => {\n        t.ctrl.start({ default: context });\n      });\n    }\n  }, [context]);\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(changes, (_, t) => {\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex((state) => state.key === t.key);\n      transitions.splice(ind, 1);\n    }\n  });\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(\n    () => {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(\n        exitingTransitions.current.size ? exitingTransitions.current : changes,\n        ({ phase, payload }, t) => {\n          const { ctrl } = t;\n          t.phase = phase;\n          ref?.add(ctrl);\n          if (hasContext && phase == \"enter\" /* ENTER */) {\n            ctrl.start({ default: context });\n          }\n          if (payload) {\n            replaceRef(ctrl, payload.ref);\n            if ((ctrl.ref || ref) && !forceChange.current) {\n              ctrl.update(payload);\n            } else {\n              ctrl.start(payload);\n              if (forceChange.current) {\n                forceChange.current = false;\n              }\n            }\n          }\n        }\n      );\n    },\n    reset ? void 0 : deps\n  );\n  const renderTransitions = (render) => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, transitions.map((t, i) => {\n    const { springs } = changes.get(t) || t.ctrl;\n    const elem = render({ ...springs }, t.item, t, i);\n    return elem && elem.type ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\n      elem.type,\n      {\n        ...elem.props,\n        key: _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.str(t.key) || _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.num(t.key) ? t.key : t.ctrl.id,\n        ref: elem.ref\n      }\n    ) : elem;\n  }));\n  return ref ? [renderTransitions, ref] : renderTransitions;\n}\nvar nextKey = 1;\nfunction getKeys(items, { key, keys = key }, prevTransitions) {\n  if (keys === null) {\n    const reused = /* @__PURE__ */ new Set();\n    return items.map((item) => {\n      const t = prevTransitions && prevTransitions.find(\n        (t2) => t2.item === item && t2.phase !== \"leave\" /* LEAVE */ && !reused.has(t2)\n      );\n      if (t) {\n        reused.add(t);\n        return t.key;\n      }\n      return nextKey++;\n    });\n  }\n  return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.und(keys) ? items : _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(keys) ? items.map(keys) : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(keys);\n}\n\n// src/hooks/useScroll.ts\n\nvar useScroll = ({\n  container,\n  ...springOptions\n} = {}) => {\n  const [scrollValues, api] = useSpring(\n    () => ({\n      scrollX: 0,\n      scrollY: 0,\n      scrollXProgress: 0,\n      scrollYProgress: 0,\n      ...springOptions\n    }),\n    []\n  );\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    const cleanupScroll = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.onScroll)(\n      ({ x, y }) => {\n        api.start({\n          scrollX: x.current,\n          scrollXProgress: x.progress,\n          scrollY: y.current,\n          scrollYProgress: y.progress\n        });\n      },\n      { container: container?.current || void 0 }\n    );\n    return () => {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(Object.values(scrollValues), (value) => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return scrollValues;\n};\n\n// src/hooks/useResize.ts\n\nvar useResize = ({\n  container,\n  ...springOptions\n}) => {\n  const [sizeValues, api] = useSpring(\n    () => ({\n      width: 0,\n      height: 0,\n      ...springOptions\n    }),\n    []\n  );\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    const cleanupScroll = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.onResize)(\n      ({ width, height }) => {\n        api.start({\n          width,\n          height,\n          immediate: sizeValues.width.get() === 0 || sizeValues.height.get() === 0\n        });\n      },\n      { container: container?.current || void 0 }\n    );\n    return () => {\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)(Object.values(sizeValues), (value) => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return sizeValues;\n};\n\n// src/hooks/useInView.ts\n\n\nvar defaultThresholdOptions = {\n  any: 0,\n  all: 1\n};\nfunction useInView(props, args) {\n  const [isInView, setIsInView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(void 0);\n  const propsFn = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(props) && props;\n  const springsProps = propsFn ? propsFn() : {};\n  const { to: to2 = {}, from = {}, ...restSpringProps } = springsProps;\n  const intersectionArguments = propsFn ? args : props;\n  const [springs, api] = useSpring(() => ({ from, ...restSpringProps }), []);\n  (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect)(() => {\n    const element = ref.current;\n    const {\n      root,\n      once,\n      amount = \"any\",\n      ...restArgs\n    } = intersectionArguments ?? {};\n    if (!element || once && isInView || typeof IntersectionObserver === \"undefined\")\n      return;\n    const activeIntersections = /* @__PURE__ */ new WeakMap();\n    const onEnter = () => {\n      if (to2) {\n        api.start(to2);\n      }\n      setIsInView(true);\n      const cleanup = () => {\n        if (from) {\n          api.start(from);\n        }\n        setIsInView(false);\n      };\n      return once ? void 0 : cleanup;\n    };\n    const handleIntersection = (entries) => {\n      entries.forEach((entry) => {\n        const onLeave = activeIntersections.get(entry.target);\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return;\n        }\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter();\n          if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave);\n          } else {\n            observer.unobserve(entry.target);\n          }\n        } else if (onLeave) {\n          onLeave();\n          activeIntersections.delete(entry.target);\n        }\n      });\n    };\n    const observer = new IntersectionObserver(handleIntersection, {\n      root: root && root.current || void 0,\n      threshold: typeof amount === \"number\" || Array.isArray(amount) ? amount : defaultThresholdOptions[amount],\n      ...restArgs\n    });\n    observer.observe(element);\n    return () => observer.unobserve(element);\n  }, [intersectionArguments]);\n  if (propsFn) {\n    return [ref, springs];\n  }\n  return [ref, isInView];\n}\n\n// src/components/Spring.tsx\nfunction Spring({ children, ...props }) {\n  return children(useSpring(props));\n}\n\n// src/components/Trail.tsx\n\nfunction Trail({\n  items,\n  children,\n  ...props\n}) {\n  const trails = useTrail(items.length, props);\n  return items.map((item, index) => {\n    const result = children(item, index);\n    return _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.fun(result) ? result(trails[index]) : result;\n  });\n}\n\n// src/components/Transition.tsx\nfunction Transition({\n  items,\n  children,\n  ...props\n}) {\n  return useTransition(items, props)(children);\n}\n\n// src/interpolate.ts\n\n\n// src/Interpolation.ts\n\n\nvar Interpolation = class extends FrameValue {\n  constructor(source, args) {\n    super();\n    this.source = source;\n    /** Equals false when in the frameloop */\n    this.idle = true;\n    /** The inputs which are currently animating */\n    this._active = /* @__PURE__ */ new Set();\n    this.calc = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.createInterpolator)(...args);\n    const value = this._get();\n    const nodeType = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimatedType)(value);\n    (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.setAnimated)(this, nodeType.create(value));\n  }\n  advance(_dt) {\n    const value = this._get();\n    const oldValue = this.get();\n    if (!(0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.isEqual)(value, oldValue)) {\n      (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getAnimated)(this).setValue(value);\n      this._onChange(value, this.idle);\n    }\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this);\n    }\n  }\n  _get() {\n    const inputs = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.is.arr(this.source) ? this.source.map(_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue) : (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.getFluidValue)(this.source));\n    return this.calc(...inputs);\n  }\n  _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false;\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getPayload)(this), (node) => {\n        node.done = false;\n      });\n      if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.skipAnimation) {\n        _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates(() => this.advance());\n        becomeIdle(this);\n      } else {\n        _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.frameLoop.start(this);\n      }\n    }\n  }\n  // Observe our sources only when we're observed.\n  _attach() {\n    let priority = 1;\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(this.source), (source) => {\n      if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(source)) {\n        (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.addFluidObserver)(source, this);\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source);\n        }\n        priority = Math.max(priority, source.priority + 1);\n      }\n    });\n    this.priority = priority;\n    this._start();\n  }\n  // Stop observing our sources once we have no observers.\n  _detach() {\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(this.source), (source) => {\n      if ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.hasFluidValue)(source)) {\n        (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.removeFluidObserver)(source, this);\n      }\n    });\n    this._active.clear();\n    becomeIdle(this);\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      if (event.idle) {\n        this.advance();\n      } else {\n        this._active.add(event.parent);\n        this._start();\n      }\n    } else if (event.type == \"idle\") {\n      this._active.delete(event.parent);\n    } else if (event.type == \"priority\") {\n      this.priority = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.toArray)(this.source).reduce(\n        (highest, parent) => Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1),\n        0\n      );\n    }\n  }\n};\nfunction isIdle(source) {\n  return source.idle !== false;\n}\nfunction checkIdle(active) {\n  return !active.size || Array.from(active).every(isIdle);\n}\nfunction becomeIdle(self) {\n  if (!self.idle) {\n    self.idle = true;\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.each)((0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_2__.getPayload)(self), (node) => {\n      node.done = true;\n    });\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.callFluidObservers)(self, {\n      type: \"idle\",\n      parent: self\n    });\n  }\n}\n\n// src/interpolate.ts\nvar to = (source, ...args) => new Interpolation(source, args);\nvar interpolate = (source, ...args) => ((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.deprecateInterpolate)(), new Interpolation(source, args));\n\n// src/globals.ts\n\n_react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.Globals.assign({\n  createStringInterpolator: _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args)\n});\nvar update = _react_spring_shared__WEBPACK_IMPORTED_MODULE_0__.frameLoop.advance;\n\n// src/index.ts\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/core/dist/react-spring_core.modern.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __raf: () => (/* binding */ __raf),\n/* harmony export */   raf: () => (/* binding */ raf)\n/* harmony export */ });\n// src/index.ts\nvar updateQueue = makeQueue();\nvar raf = (fn) => schedule(fn, updateQueue);\nvar writeQueue = makeQueue();\nraf.write = (fn) => schedule(fn, writeQueue);\nvar onStartQueue = makeQueue();\nraf.onStart = (fn) => schedule(fn, onStartQueue);\nvar onFrameQueue = makeQueue();\nraf.onFrame = (fn) => schedule(fn, onFrameQueue);\nvar onFinishQueue = makeQueue();\nraf.onFinish = (fn) => schedule(fn, onFinishQueue);\nvar timeouts = [];\nraf.setTimeout = (handler, ms) => {\n  const time = raf.now() + ms;\n  const cancel = () => {\n    const i = timeouts.findIndex((t) => t.cancel == cancel);\n    if (~i) timeouts.splice(i, 1);\n    pendingCount -= ~i ? 1 : 0;\n  };\n  const timeout = { time, handler, cancel };\n  timeouts.splice(findTimeout(time), 0, timeout);\n  pendingCount += 1;\n  start();\n  return timeout;\n};\nvar findTimeout = (time) => ~(~timeouts.findIndex((t) => t.time > time) || ~timeouts.length);\nraf.cancel = (fn) => {\n  onStartQueue.delete(fn);\n  onFrameQueue.delete(fn);\n  onFinishQueue.delete(fn);\n  updateQueue.delete(fn);\n  writeQueue.delete(fn);\n};\nraf.sync = (fn) => {\n  sync = true;\n  raf.batchedUpdates(fn);\n  sync = false;\n};\nraf.throttle = (fn) => {\n  let lastArgs;\n  function queuedFn() {\n    try {\n      fn(...lastArgs);\n    } finally {\n      lastArgs = null;\n    }\n  }\n  function throttled(...args) {\n    lastArgs = args;\n    raf.onStart(queuedFn);\n  }\n  throttled.handler = fn;\n  throttled.cancel = () => {\n    onStartQueue.delete(queuedFn);\n    lastArgs = null;\n  };\n  return throttled;\n};\nvar nativeRaf = typeof window != \"undefined\" ? window.requestAnimationFrame : (\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  () => {\n  }\n);\nraf.use = (impl) => nativeRaf = impl;\nraf.now = typeof performance != \"undefined\" ? () => performance.now() : Date.now;\nraf.batchedUpdates = (fn) => fn();\nraf.catch = console.error;\nraf.frameLoop = \"always\";\nraf.advance = () => {\n  if (raf.frameLoop !== \"demand\") {\n    console.warn(\n      \"Cannot call the manual advancement of rafz whilst frameLoop is not set as demand\"\n    );\n  } else {\n    update();\n  }\n};\nvar ts = -1;\nvar pendingCount = 0;\nvar sync = false;\nfunction schedule(fn, queue) {\n  if (sync) {\n    queue.delete(fn);\n    fn(0);\n  } else {\n    queue.add(fn);\n    start();\n  }\n}\nfunction start() {\n  if (ts < 0) {\n    ts = 0;\n    if (raf.frameLoop !== \"demand\") {\n      nativeRaf(loop);\n    }\n  }\n}\nfunction stop() {\n  ts = -1;\n}\nfunction loop() {\n  if (~ts) {\n    nativeRaf(loop);\n    raf.batchedUpdates(update);\n  }\n}\nfunction update() {\n  const prevTs = ts;\n  ts = raf.now();\n  const count = findTimeout(ts);\n  if (count) {\n    eachSafely(timeouts.splice(0, count), (t) => t.handler());\n    pendingCount -= count;\n  }\n  if (!pendingCount) {\n    stop();\n    return;\n  }\n  onStartQueue.flush();\n  updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667);\n  onFrameQueue.flush();\n  writeQueue.flush();\n  onFinishQueue.flush();\n}\nfunction makeQueue() {\n  let next = /* @__PURE__ */ new Set();\n  let current = next;\n  return {\n    add(fn) {\n      pendingCount += current == next && !next.has(fn) ? 1 : 0;\n      next.add(fn);\n    },\n    delete(fn) {\n      pendingCount -= current == next && next.has(fn) ? 1 : 0;\n      return next.delete(fn);\n    },\n    flush(arg) {\n      if (current.size) {\n        next = /* @__PURE__ */ new Set();\n        pendingCount -= current.size;\n        eachSafely(current, (fn) => fn(arg) && next.add(fn));\n        pendingCount += next.size;\n        current = next;\n      }\n    }\n  };\n}\nfunction eachSafely(values, each) {\n  values.forEach((value) => {\n    try {\n      each(value);\n    } catch (e) {\n      raf.catch(e);\n    }\n  });\n}\nvar __raf = {\n  /** The number of pending tasks */\n  count() {\n    return pendingCount;\n  },\n  /** Whether there's a raf update loop running */\n  isRunning() {\n    return ts >= 0;\n  },\n  /** Clear internal state. Never call from update loop! */\n  clear() {\n    ts = -1;\n    timeouts = [];\n    onStartQueue = makeQueue();\n    updateQueue = makeQueue();\n    onFrameQueue = makeQueue();\n    writeQueue = makeQueue();\n    onFinishQueue = makeQueue();\n    pendingCount = 0;\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FluidValue: () => (/* binding */ FluidValue),\n/* harmony export */   Globals: () => (/* binding */ globals_exports),\n/* harmony export */   addFluidObserver: () => (/* binding */ addFluidObserver),\n/* harmony export */   callFluidObserver: () => (/* binding */ callFluidObserver),\n/* harmony export */   callFluidObservers: () => (/* binding */ callFluidObservers),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   colorToRgba: () => (/* binding */ colorToRgba),\n/* harmony export */   colors: () => (/* binding */ colors2),\n/* harmony export */   createInterpolator: () => (/* binding */ createInterpolator),\n/* harmony export */   createStringInterpolator: () => (/* binding */ createStringInterpolator2),\n/* harmony export */   defineHidden: () => (/* binding */ defineHidden),\n/* harmony export */   deprecateDirectCall: () => (/* binding */ deprecateDirectCall),\n/* harmony export */   deprecateInterpolate: () => (/* binding */ deprecateInterpolate),\n/* harmony export */   each: () => (/* binding */ each),\n/* harmony export */   eachProp: () => (/* binding */ eachProp),\n/* harmony export */   easings: () => (/* binding */ easings),\n/* harmony export */   flush: () => (/* binding */ flush),\n/* harmony export */   flushCalls: () => (/* binding */ flushCalls),\n/* harmony export */   frameLoop: () => (/* binding */ frameLoop),\n/* harmony export */   getFluidObservers: () => (/* binding */ getFluidObservers),\n/* harmony export */   getFluidValue: () => (/* binding */ getFluidValue),\n/* harmony export */   hasFluidValue: () => (/* binding */ hasFluidValue),\n/* harmony export */   hex3: () => (/* binding */ hex3),\n/* harmony export */   hex4: () => (/* binding */ hex4),\n/* harmony export */   hex6: () => (/* binding */ hex6),\n/* harmony export */   hex8: () => (/* binding */ hex8),\n/* harmony export */   hsl: () => (/* binding */ hsl),\n/* harmony export */   hsla: () => (/* binding */ hsla),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   isAnimatedString: () => (/* binding */ isAnimatedString),\n/* harmony export */   isEqual: () => (/* binding */ isEqual),\n/* harmony export */   isSSR: () => (/* binding */ isSSR),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   onResize: () => (/* binding */ onResize),\n/* harmony export */   onScroll: () => (/* binding */ onScroll),\n/* harmony export */   once: () => (/* binding */ once),\n/* harmony export */   prefix: () => (/* binding */ prefix),\n/* harmony export */   raf: () => (/* reexport safe */ _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf),\n/* harmony export */   removeFluidObserver: () => (/* binding */ removeFluidObserver),\n/* harmony export */   rgb: () => (/* binding */ rgb),\n/* harmony export */   rgba: () => (/* binding */ rgba),\n/* harmony export */   setFluidGetter: () => (/* binding */ setFluidGetter),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   useConstant: () => (/* binding */ useConstant),\n/* harmony export */   useForceUpdate: () => (/* binding */ useForceUpdate),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useMemoOne: () => (/* binding */ useMemoOne),\n/* harmony export */   useOnce: () => (/* binding */ useOnce),\n/* harmony export */   usePrev: () => (/* binding */ usePrev),\n/* harmony export */   useReducedMotion: () => (/* binding */ useReducedMotion)\n/* harmony export */ });\n/* harmony import */ var _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-spring/rafz */ \"(ssr)/./node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/globals.ts\nvar globals_exports = {};\n__export(globals_exports, {\n  assign: () => assign,\n  colors: () => colors,\n  createStringInterpolator: () => createStringInterpolator,\n  skipAnimation: () => skipAnimation,\n  to: () => to,\n  willAdvance: () => willAdvance\n});\n\n\n// src/helpers.ts\nfunction noop() {\n}\nvar defineHidden = (obj, key, value) => Object.defineProperty(obj, key, { value, writable: true, configurable: true });\nvar is = {\n  arr: Array.isArray,\n  obj: (a) => !!a && a.constructor.name === \"Object\",\n  fun: (a) => typeof a === \"function\",\n  str: (a) => typeof a === \"string\",\n  num: (a) => typeof a === \"number\",\n  und: (a) => a === void 0\n};\nfunction isEqual(a, b) {\n  if (is.arr(a)) {\n    if (!is.arr(b) || a.length !== b.length) return false;\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) return false;\n    }\n    return true;\n  }\n  return a === b;\n}\nvar each = (obj, fn) => obj.forEach(fn);\nfunction eachProp(obj, fn, ctx) {\n  if (is.arr(obj)) {\n    for (let i = 0; i < obj.length; i++) {\n      fn.call(ctx, obj[i], `${i}`);\n    }\n    return;\n  }\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      fn.call(ctx, obj[key], key);\n    }\n  }\n}\nvar toArray = (a) => is.und(a) ? [] : is.arr(a) ? a : [a];\nfunction flush(queue, iterator) {\n  if (queue.size) {\n    const items = Array.from(queue);\n    queue.clear();\n    each(items, iterator);\n  }\n}\nvar flushCalls = (queue, ...args) => flush(queue, (fn) => fn(...args));\nvar isSSR = () => typeof window === \"undefined\" || !window.navigator || /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent);\n\n// src/globals.ts\nvar createStringInterpolator;\nvar to;\nvar colors = null;\nvar skipAnimation = false;\nvar willAdvance = noop;\nvar assign = (globals) => {\n  if (globals.to) to = globals.to;\n  if (globals.now) _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.now = globals.now;\n  if (globals.colors !== void 0) colors = globals.colors;\n  if (globals.skipAnimation != null) skipAnimation = globals.skipAnimation;\n  if (globals.createStringInterpolator)\n    createStringInterpolator = globals.createStringInterpolator;\n  if (globals.requestAnimationFrame) _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.use(globals.requestAnimationFrame);\n  if (globals.batchedUpdates) _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.batchedUpdates = globals.batchedUpdates;\n  if (globals.willAdvance) willAdvance = globals.willAdvance;\n  if (globals.frameLoop) _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.frameLoop = globals.frameLoop;\n};\n\n// src/FrameLoop.ts\n\nvar startQueue = /* @__PURE__ */ new Set();\nvar currentFrame = [];\nvar prevFrame = [];\nvar priority = 0;\nvar frameLoop = {\n  get idle() {\n    return !startQueue.size && !currentFrame.length;\n  },\n  /** Advance the given animation on every frame until idle. */\n  start(animation) {\n    if (priority > animation.priority) {\n      startQueue.add(animation);\n      _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.onStart(flushStartQueue);\n    } else {\n      startSafely(animation);\n      (0,_react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf)(advance);\n    }\n  },\n  /** Advance all animations by the given time. */\n  advance,\n  /** Call this when an animation's priority changes. */\n  sort(animation) {\n    if (priority) {\n      _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.onFrame(() => frameLoop.sort(animation));\n    } else {\n      const prevIndex = currentFrame.indexOf(animation);\n      if (~prevIndex) {\n        currentFrame.splice(prevIndex, 1);\n        startUnsafely(animation);\n      }\n    }\n  },\n  /**\n   * Clear all animations. For testing purposes.\n   *\n   * ☠️ Never call this from within the frameloop.\n   */\n  clear() {\n    currentFrame = [];\n    startQueue.clear();\n  }\n};\nfunction flushStartQueue() {\n  startQueue.forEach(startSafely);\n  startQueue.clear();\n  (0,_react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf)(advance);\n}\nfunction startSafely(animation) {\n  if (!currentFrame.includes(animation)) startUnsafely(animation);\n}\nfunction startUnsafely(animation) {\n  currentFrame.splice(\n    findIndex(currentFrame, (other) => other.priority > animation.priority),\n    0,\n    animation\n  );\n}\nfunction advance(dt) {\n  const nextFrame = prevFrame;\n  for (let i = 0; i < currentFrame.length; i++) {\n    const animation = currentFrame[i];\n    priority = animation.priority;\n    if (!animation.idle) {\n      willAdvance(animation);\n      animation.advance(dt);\n      if (!animation.idle) {\n        nextFrame.push(animation);\n      }\n    }\n  }\n  priority = 0;\n  prevFrame = currentFrame;\n  prevFrame.length = 0;\n  currentFrame = nextFrame;\n  return currentFrame.length > 0;\n}\nfunction findIndex(arr, test) {\n  const index = arr.findIndex(test);\n  return index < 0 ? arr.length : index;\n}\n\n// src/clamp.ts\nvar clamp = (min, max, v) => Math.min(Math.max(v, min), max);\n\n// src/colors.ts\nvar colors2 = {\n  transparent: 0,\n  aliceblue: 4042850303,\n  antiquewhite: 4209760255,\n  aqua: 16777215,\n  aquamarine: 2147472639,\n  azure: 4043309055,\n  beige: 4126530815,\n  bisque: 4293182719,\n  black: 255,\n  blanchedalmond: 4293643775,\n  blue: 65535,\n  blueviolet: 2318131967,\n  brown: 2771004159,\n  burlywood: 3736635391,\n  burntsienna: 3934150143,\n  cadetblue: 1604231423,\n  chartreuse: 2147418367,\n  chocolate: 3530104575,\n  coral: 4286533887,\n  cornflowerblue: 1687547391,\n  cornsilk: 4294499583,\n  crimson: 3692313855,\n  cyan: 16777215,\n  darkblue: 35839,\n  darkcyan: 9145343,\n  darkgoldenrod: 3095792639,\n  darkgray: 2846468607,\n  darkgreen: 6553855,\n  darkgrey: 2846468607,\n  darkkhaki: 3182914559,\n  darkmagenta: 2332068863,\n  darkolivegreen: 1433087999,\n  darkorange: 4287365375,\n  darkorchid: 2570243327,\n  darkred: 2332033279,\n  darksalmon: 3918953215,\n  darkseagreen: 2411499519,\n  darkslateblue: 1211993087,\n  darkslategray: 793726975,\n  darkslategrey: 793726975,\n  darkturquoise: 13554175,\n  darkviolet: 2483082239,\n  deeppink: 4279538687,\n  deepskyblue: 12582911,\n  dimgray: 1768516095,\n  dimgrey: 1768516095,\n  dodgerblue: 512819199,\n  firebrick: 2988581631,\n  floralwhite: 4294635775,\n  forestgreen: 579543807,\n  fuchsia: 4278255615,\n  gainsboro: 3705462015,\n  ghostwhite: 4177068031,\n  gold: 4292280575,\n  goldenrod: 3668254975,\n  gray: 2155905279,\n  green: 8388863,\n  greenyellow: 2919182335,\n  grey: 2155905279,\n  honeydew: 4043305215,\n  hotpink: 4285117695,\n  indianred: 3445382399,\n  indigo: 1258324735,\n  ivory: 4294963455,\n  khaki: 4041641215,\n  lavender: 3873897215,\n  lavenderblush: 4293981695,\n  lawngreen: 2096890111,\n  lemonchiffon: 4294626815,\n  lightblue: 2916673279,\n  lightcoral: 4034953471,\n  lightcyan: 3774873599,\n  lightgoldenrodyellow: 4210742015,\n  lightgray: 3553874943,\n  lightgreen: 2431553791,\n  lightgrey: 3553874943,\n  lightpink: 4290167295,\n  lightsalmon: 4288707327,\n  lightseagreen: 548580095,\n  lightskyblue: 2278488831,\n  lightslategray: 2005441023,\n  lightslategrey: 2005441023,\n  lightsteelblue: 2965692159,\n  lightyellow: 4294959359,\n  lime: 16711935,\n  limegreen: 852308735,\n  linen: 4210091775,\n  magenta: 4278255615,\n  maroon: 2147483903,\n  mediumaquamarine: 1724754687,\n  mediumblue: 52735,\n  mediumorchid: 3126187007,\n  mediumpurple: 2473647103,\n  mediumseagreen: 1018393087,\n  mediumslateblue: 2070474495,\n  mediumspringgreen: 16423679,\n  mediumturquoise: 1221709055,\n  mediumvioletred: 3340076543,\n  midnightblue: 421097727,\n  mintcream: 4127193855,\n  mistyrose: 4293190143,\n  moccasin: 4293178879,\n  navajowhite: 4292783615,\n  navy: 33023,\n  oldlace: 4260751103,\n  olive: 2155872511,\n  olivedrab: 1804477439,\n  orange: 4289003775,\n  orangered: 4282712319,\n  orchid: 3664828159,\n  palegoldenrod: 4008225535,\n  palegreen: 2566625535,\n  paleturquoise: 2951671551,\n  palevioletred: 3681588223,\n  papayawhip: 4293907967,\n  peachpuff: 4292524543,\n  peru: 3448061951,\n  pink: 4290825215,\n  plum: 3718307327,\n  powderblue: 2967529215,\n  purple: 2147516671,\n  rebeccapurple: 1714657791,\n  red: 4278190335,\n  rosybrown: 3163525119,\n  royalblue: 1097458175,\n  saddlebrown: 2336560127,\n  salmon: 4202722047,\n  sandybrown: 4104413439,\n  seagreen: 780883967,\n  seashell: 4294307583,\n  sienna: 2689740287,\n  silver: 3233857791,\n  skyblue: 2278484991,\n  slateblue: 1784335871,\n  slategray: 1887473919,\n  slategrey: 1887473919,\n  snow: 4294638335,\n  springgreen: 16744447,\n  steelblue: 1182971135,\n  tan: 3535047935,\n  teal: 8421631,\n  thistle: 3636451583,\n  tomato: 4284696575,\n  turquoise: 1088475391,\n  violet: 4001558271,\n  wheat: 4125012991,\n  white: 4294967295,\n  whitesmoke: 4126537215,\n  yellow: 4294902015,\n  yellowgreen: 2597139199\n};\n\n// src/colorMatchers.ts\nvar NUMBER = \"[-+]?\\\\d*\\\\.?\\\\d+\";\nvar PERCENTAGE = NUMBER + \"%\";\nfunction call(...parts) {\n  return \"\\\\(\\\\s*(\" + parts.join(\")\\\\s*,\\\\s*(\") + \")\\\\s*\\\\)\";\n}\nvar rgb = new RegExp(\"rgb\" + call(NUMBER, NUMBER, NUMBER));\nvar rgba = new RegExp(\"rgba\" + call(NUMBER, NUMBER, NUMBER, NUMBER));\nvar hsl = new RegExp(\"hsl\" + call(NUMBER, PERCENTAGE, PERCENTAGE));\nvar hsla = new RegExp(\n  \"hsla\" + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER)\n);\nvar hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nvar hex4 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nvar hex6 = /^#([0-9a-fA-F]{6})$/;\nvar hex8 = /^#([0-9a-fA-F]{8})$/;\n\n// src/normalizeColor.ts\nfunction normalizeColor(color) {\n  let match;\n  if (typeof color === \"number\") {\n    return color >>> 0 === color && color >= 0 && color <= 4294967295 ? color : null;\n  }\n  if (match = hex6.exec(color))\n    return parseInt(match[1] + \"ff\", 16) >>> 0;\n  if (colors && colors[color] !== void 0) {\n    return colors[color];\n  }\n  if (match = rgb.exec(color)) {\n    return (parse255(match[1]) << 24 | // r\n    parse255(match[2]) << 16 | // g\n    parse255(match[3]) << 8 | // b\n    255) >>> // a\n    0;\n  }\n  if (match = rgba.exec(color)) {\n    return (parse255(match[1]) << 24 | // r\n    parse255(match[2]) << 16 | // g\n    parse255(match[3]) << 8 | // b\n    parse1(match[4])) >>> // a\n    0;\n  }\n  if (match = hex3.exec(color)) {\n    return parseInt(\n      match[1] + match[1] + // r\n      match[2] + match[2] + // g\n      match[3] + match[3] + // b\n      \"ff\",\n      // a\n      16\n    ) >>> 0;\n  }\n  if (match = hex8.exec(color)) return parseInt(match[1], 16) >>> 0;\n  if (match = hex4.exec(color)) {\n    return parseInt(\n      match[1] + match[1] + // r\n      match[2] + match[2] + // g\n      match[3] + match[3] + // b\n      match[4] + match[4],\n      // a\n      16\n    ) >>> 0;\n  }\n  if (match = hsl.exec(color)) {\n    return (hslToRgb(\n      parse360(match[1]),\n      // h\n      parsePercentage(match[2]),\n      // s\n      parsePercentage(match[3])\n      // l\n    ) | 255) >>> // a\n    0;\n  }\n  if (match = hsla.exec(color)) {\n    return (hslToRgb(\n      parse360(match[1]),\n      // h\n      parsePercentage(match[2]),\n      // s\n      parsePercentage(match[3])\n      // l\n    ) | parse1(match[4])) >>> // a\n    0;\n  }\n  return null;\n}\nfunction hue2rgb(p, q, t) {\n  if (t < 0) t += 1;\n  if (t > 1) t -= 1;\n  if (t < 1 / 6) return p + (q - p) * 6 * t;\n  if (t < 1 / 2) return q;\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n  return p;\n}\nfunction hslToRgb(h, s, l) {\n  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n  const p = 2 * l - q;\n  const r = hue2rgb(p, q, h + 1 / 3);\n  const g = hue2rgb(p, q, h);\n  const b = hue2rgb(p, q, h - 1 / 3);\n  return Math.round(r * 255) << 24 | Math.round(g * 255) << 16 | Math.round(b * 255) << 8;\n}\nfunction parse255(str) {\n  const int = parseInt(str, 10);\n  if (int < 0) return 0;\n  if (int > 255) return 255;\n  return int;\n}\nfunction parse360(str) {\n  const int = parseFloat(str);\n  return (int % 360 + 360) % 360 / 360;\n}\nfunction parse1(str) {\n  const num = parseFloat(str);\n  if (num < 0) return 0;\n  if (num > 1) return 255;\n  return Math.round(num * 255);\n}\nfunction parsePercentage(str) {\n  const int = parseFloat(str);\n  if (int < 0) return 0;\n  if (int > 100) return 1;\n  return int / 100;\n}\n\n// src/colorToRgba.ts\nfunction colorToRgba(input) {\n  let int32Color = normalizeColor(input);\n  if (int32Color === null) return input;\n  int32Color = int32Color || 0;\n  const r = (int32Color & 4278190080) >>> 24;\n  const g = (int32Color & 16711680) >>> 16;\n  const b = (int32Color & 65280) >>> 8;\n  const a = (int32Color & 255) / 255;\n  return `rgba(${r}, ${g}, ${b}, ${a})`;\n}\n\n// src/createInterpolator.ts\nvar createInterpolator = (range, output, extrapolate) => {\n  if (is.fun(range)) {\n    return range;\n  }\n  if (is.arr(range)) {\n    return createInterpolator({\n      range,\n      output,\n      extrapolate\n    });\n  }\n  if (is.str(range.output[0])) {\n    return createStringInterpolator(range);\n  }\n  const config = range;\n  const outputRange = config.output;\n  const inputRange = config.range || [0, 1];\n  const extrapolateLeft = config.extrapolateLeft || config.extrapolate || \"extend\";\n  const extrapolateRight = config.extrapolateRight || config.extrapolate || \"extend\";\n  const easing = config.easing || ((t) => t);\n  return (input) => {\n    const range2 = findRange(input, inputRange);\n    return interpolate(\n      input,\n      inputRange[range2],\n      inputRange[range2 + 1],\n      outputRange[range2],\n      outputRange[range2 + 1],\n      easing,\n      extrapolateLeft,\n      extrapolateRight,\n      config.map\n    );\n  };\n};\nfunction interpolate(input, inputMin, inputMax, outputMin, outputMax, easing, extrapolateLeft, extrapolateRight, map) {\n  let result = map ? map(input) : input;\n  if (result < inputMin) {\n    if (extrapolateLeft === \"identity\") return result;\n    else if (extrapolateLeft === \"clamp\") result = inputMin;\n  }\n  if (result > inputMax) {\n    if (extrapolateRight === \"identity\") return result;\n    else if (extrapolateRight === \"clamp\") result = inputMax;\n  }\n  if (outputMin === outputMax) return outputMin;\n  if (inputMin === inputMax) return input <= inputMin ? outputMin : outputMax;\n  if (inputMin === -Infinity) result = -result;\n  else if (inputMax === Infinity) result = result - inputMin;\n  else result = (result - inputMin) / (inputMax - inputMin);\n  result = easing(result);\n  if (outputMin === -Infinity) result = -result;\n  else if (outputMax === Infinity) result = result + outputMin;\n  else result = result * (outputMax - outputMin) + outputMin;\n  return result;\n}\nfunction findRange(input, inputRange) {\n  for (var i = 1; i < inputRange.length - 1; ++i)\n    if (inputRange[i] >= input) break;\n  return i - 1;\n}\n\n// src/easings.ts\nvar steps = (steps2, direction = \"end\") => (progress2) => {\n  progress2 = direction === \"end\" ? Math.min(progress2, 0.999) : Math.max(progress2, 1e-3);\n  const expanded = progress2 * steps2;\n  const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n  return clamp(0, 1, rounded / steps2);\n};\nvar c1 = 1.70158;\nvar c2 = c1 * 1.525;\nvar c3 = c1 + 1;\nvar c4 = 2 * Math.PI / 3;\nvar c5 = 2 * Math.PI / 4.5;\nvar bounceOut = (x) => {\n  const n1 = 7.5625;\n  const d1 = 2.75;\n  if (x < 1 / d1) {\n    return n1 * x * x;\n  } else if (x < 2 / d1) {\n    return n1 * (x -= 1.5 / d1) * x + 0.75;\n  } else if (x < 2.5 / d1) {\n    return n1 * (x -= 2.25 / d1) * x + 0.9375;\n  } else {\n    return n1 * (x -= 2.625 / d1) * x + 0.984375;\n  }\n};\nvar easings = {\n  linear: (x) => x,\n  easeInQuad: (x) => x * x,\n  easeOutQuad: (x) => 1 - (1 - x) * (1 - x),\n  easeInOutQuad: (x) => x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2,\n  easeInCubic: (x) => x * x * x,\n  easeOutCubic: (x) => 1 - Math.pow(1 - x, 3),\n  easeInOutCubic: (x) => x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2,\n  easeInQuart: (x) => x * x * x * x,\n  easeOutQuart: (x) => 1 - Math.pow(1 - x, 4),\n  easeInOutQuart: (x) => x < 0.5 ? 8 * x * x * x * x : 1 - Math.pow(-2 * x + 2, 4) / 2,\n  easeInQuint: (x) => x * x * x * x * x,\n  easeOutQuint: (x) => 1 - Math.pow(1 - x, 5),\n  easeInOutQuint: (x) => x < 0.5 ? 16 * x * x * x * x * x : 1 - Math.pow(-2 * x + 2, 5) / 2,\n  easeInSine: (x) => 1 - Math.cos(x * Math.PI / 2),\n  easeOutSine: (x) => Math.sin(x * Math.PI / 2),\n  easeInOutSine: (x) => -(Math.cos(Math.PI * x) - 1) / 2,\n  easeInExpo: (x) => x === 0 ? 0 : Math.pow(2, 10 * x - 10),\n  easeOutExpo: (x) => x === 1 ? 1 : 1 - Math.pow(2, -10 * x),\n  easeInOutExpo: (x) => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? Math.pow(2, 20 * x - 10) / 2 : (2 - Math.pow(2, -20 * x + 10)) / 2,\n  easeInCirc: (x) => 1 - Math.sqrt(1 - Math.pow(x, 2)),\n  easeOutCirc: (x) => Math.sqrt(1 - Math.pow(x - 1, 2)),\n  easeInOutCirc: (x) => x < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2,\n  easeInBack: (x) => c3 * x * x * x - c1 * x * x,\n  easeOutBack: (x) => 1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2),\n  easeInOutBack: (x) => x < 0.5 ? Math.pow(2 * x, 2) * ((c2 + 1) * 2 * x - c2) / 2 : (Math.pow(2 * x - 2, 2) * ((c2 + 1) * (x * 2 - 2) + c2) + 2) / 2,\n  easeInElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : -Math.pow(2, 10 * x - 10) * Math.sin((x * 10 - 10.75) * c4),\n  easeOutElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1,\n  easeInOutElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? -(Math.pow(2, 20 * x - 10) * Math.sin((20 * x - 11.125) * c5)) / 2 : Math.pow(2, -20 * x + 10) * Math.sin((20 * x - 11.125) * c5) / 2 + 1,\n  easeInBounce: (x) => 1 - bounceOut(1 - x),\n  easeOutBounce: bounceOut,\n  easeInOutBounce: (x) => x < 0.5 ? (1 - bounceOut(1 - 2 * x)) / 2 : (1 + bounceOut(2 * x - 1)) / 2,\n  steps\n};\n\n// src/fluids.ts\nvar $get = Symbol.for(\"FluidValue.get\");\nvar $observers = Symbol.for(\"FluidValue.observers\");\nvar hasFluidValue = (arg) => Boolean(arg && arg[$get]);\nvar getFluidValue = (arg) => arg && arg[$get] ? arg[$get]() : arg;\nvar getFluidObservers = (target) => target[$observers] || null;\nfunction callFluidObserver(observer2, event) {\n  if (observer2.eventObserved) {\n    observer2.eventObserved(event);\n  } else {\n    observer2(event);\n  }\n}\nfunction callFluidObservers(target, event) {\n  const observers = target[$observers];\n  if (observers) {\n    observers.forEach((observer2) => {\n      callFluidObserver(observer2, event);\n    });\n  }\n}\n$get, $observers;\nvar FluidValue = class {\n  constructor(get) {\n    if (!get && !(get = this.get)) {\n      throw Error(\"Unknown getter\");\n    }\n    setFluidGetter(this, get);\n  }\n};\nvar setFluidGetter = (target, get) => setHidden(target, $get, get);\nfunction addFluidObserver(target, observer2) {\n  if (target[$get]) {\n    let observers = target[$observers];\n    if (!observers) {\n      setHidden(target, $observers, observers = /* @__PURE__ */ new Set());\n    }\n    if (!observers.has(observer2)) {\n      observers.add(observer2);\n      if (target.observerAdded) {\n        target.observerAdded(observers.size, observer2);\n      }\n    }\n  }\n  return observer2;\n}\nfunction removeFluidObserver(target, observer2) {\n  const observers = target[$observers];\n  if (observers && observers.has(observer2)) {\n    const count = observers.size - 1;\n    if (count) {\n      observers.delete(observer2);\n    } else {\n      target[$observers] = null;\n    }\n    if (target.observerRemoved) {\n      target.observerRemoved(count, observer2);\n    }\n  }\n}\nvar setHidden = (target, key, value) => Object.defineProperty(target, key, {\n  value,\n  writable: true,\n  configurable: true\n});\n\n// src/regexs.ts\nvar numberRegex = /[+\\-]?(?:0|[1-9]\\d*)(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g;\nvar colorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d\\.]+%?\\))/gi;\nvar unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, \"i\");\nvar rgbaRegex = /rgba\\(([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+)\\)/gi;\nvar cssVariableRegex = /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/;\n\n// src/variableToRgba.ts\nvar variableToRgba = (input) => {\n  const [token, fallback] = parseCSSVariable(input);\n  if (!token || isSSR()) {\n    return input;\n  }\n  const value = window.getComputedStyle(document.documentElement).getPropertyValue(token);\n  if (value) {\n    return value.trim();\n  } else if (fallback && fallback.startsWith(\"--\")) {\n    const value2 = window.getComputedStyle(document.documentElement).getPropertyValue(fallback);\n    if (value2) {\n      return value2;\n    } else {\n      return input;\n    }\n  } else if (fallback && cssVariableRegex.test(fallback)) {\n    return variableToRgba(fallback);\n  } else if (fallback) {\n    return fallback;\n  }\n  return input;\n};\nvar parseCSSVariable = (current) => {\n  const match = cssVariableRegex.exec(current);\n  if (!match) return [,];\n  const [, token, fallback] = match;\n  return [token, fallback];\n};\n\n// src/stringInterpolation.ts\nvar namedColorRegex;\nvar rgbaRound = (_, p1, p2, p3, p4) => `rgba(${Math.round(p1)}, ${Math.round(p2)}, ${Math.round(p3)}, ${p4})`;\nvar createStringInterpolator2 = (config) => {\n  if (!namedColorRegex)\n    namedColorRegex = colors ? (\n      // match color names, ignore partial matches\n      new RegExp(`(${Object.keys(colors).join(\"|\")})(?!\\\\w)`, \"g\")\n    ) : (\n      // never match\n      /^\\b$/\n    );\n  const output = config.output.map((value) => {\n    return getFluidValue(value).replace(cssVariableRegex, variableToRgba).replace(colorRegex, colorToRgba).replace(namedColorRegex, colorToRgba);\n  });\n  const keyframes = output.map((value) => value.match(numberRegex).map(Number));\n  const outputRanges = keyframes[0].map(\n    (_, i) => keyframes.map((values) => {\n      if (!(i in values)) {\n        throw Error('The arity of each \"output\" value must be equal');\n      }\n      return values[i];\n    })\n  );\n  const interpolators = outputRanges.map(\n    (output2) => createInterpolator({ ...config, output: output2 })\n  );\n  return (input) => {\n    const missingUnit = !unitRegex.test(output[0]) && output.find((value) => unitRegex.test(value))?.replace(numberRegex, \"\");\n    let i = 0;\n    return output[0].replace(\n      numberRegex,\n      () => `${interpolators[i++](input)}${missingUnit || \"\"}`\n    ).replace(rgbaRegex, rgbaRound);\n  };\n};\n\n// src/deprecations.ts\nvar prefix = \"react-spring: \";\nvar once = (fn) => {\n  const func = fn;\n  let called = false;\n  if (typeof func != \"function\") {\n    throw new TypeError(`${prefix}once requires a function parameter`);\n  }\n  return (...args) => {\n    if (!called) {\n      func(...args);\n      called = true;\n    }\n  };\n};\nvar warnInterpolate = once(console.warn);\nfunction deprecateInterpolate() {\n  warnInterpolate(\n    `${prefix}The \"interpolate\" function is deprecated in v9 (use \"to\" instead)`\n  );\n}\nvar warnDirectCall = once(console.warn);\nfunction deprecateDirectCall() {\n  warnDirectCall(\n    `${prefix}Directly calling start instead of using the api object is deprecated in v9 (use \".start\" instead), this will be removed in later 0.X.0 versions`\n  );\n}\n\n// src/isAnimatedString.ts\nfunction isAnimatedString(value) {\n  return is.str(value) && (value[0] == \"#\" || /\\d/.test(value) || // Do not identify a CSS variable as an AnimatedString if its SSR\n  !isSSR() && cssVariableRegex.test(value) || value in (colors || {}));\n}\n\n// src/dom-events/scroll/index.ts\n\n\n// src/dom-events/resize/resizeElement.ts\nvar observer;\nvar resizeHandlers = /* @__PURE__ */ new WeakMap();\nvar handleObservation = (entries) => entries.forEach(({ target, contentRect }) => {\n  return resizeHandlers.get(target)?.forEach((handler) => handler(contentRect));\n});\nfunction resizeElement(handler, target) {\n  if (!observer) {\n    if (typeof ResizeObserver !== \"undefined\") {\n      observer = new ResizeObserver(handleObservation);\n    }\n  }\n  let elementHandlers = resizeHandlers.get(target);\n  if (!elementHandlers) {\n    elementHandlers = /* @__PURE__ */ new Set();\n    resizeHandlers.set(target, elementHandlers);\n  }\n  elementHandlers.add(handler);\n  if (observer) {\n    observer.observe(target);\n  }\n  return () => {\n    const elementHandlers2 = resizeHandlers.get(target);\n    if (!elementHandlers2) return;\n    elementHandlers2.delete(handler);\n    if (!elementHandlers2.size && observer) {\n      observer.unobserve(target);\n    }\n  };\n}\n\n// src/dom-events/resize/resizeWindow.ts\nvar listeners = /* @__PURE__ */ new Set();\nvar cleanupWindowResizeHandler;\nvar createResizeHandler = () => {\n  const handleResize = () => {\n    listeners.forEach(\n      (callback) => callback({\n        width: window.innerWidth,\n        height: window.innerHeight\n      })\n    );\n  };\n  window.addEventListener(\"resize\", handleResize);\n  return () => {\n    window.removeEventListener(\"resize\", handleResize);\n  };\n};\nvar resizeWindow = (callback) => {\n  listeners.add(callback);\n  if (!cleanupWindowResizeHandler) {\n    cleanupWindowResizeHandler = createResizeHandler();\n  }\n  return () => {\n    listeners.delete(callback);\n    if (!listeners.size && cleanupWindowResizeHandler) {\n      cleanupWindowResizeHandler();\n      cleanupWindowResizeHandler = void 0;\n    }\n  };\n};\n\n// src/dom-events/resize/index.ts\nvar onResize = (callback, { container = document.documentElement } = {}) => {\n  if (container === document.documentElement) {\n    return resizeWindow(callback);\n  } else {\n    return resizeElement(callback, container);\n  }\n};\n\n// src/progress.ts\nvar progress = (min, max, value) => max - min === 0 ? 1 : (value - min) / (max - min);\n\n// src/dom-events/scroll/ScrollHandler.ts\nvar SCROLL_KEYS = {\n  x: {\n    length: \"Width\",\n    position: \"Left\"\n  },\n  y: {\n    length: \"Height\",\n    position: \"Top\"\n  }\n};\nvar ScrollHandler = class {\n  constructor(callback, container) {\n    this.createAxis = () => ({\n      current: 0,\n      progress: 0,\n      scrollLength: 0\n    });\n    this.updateAxis = (axisName) => {\n      const axis = this.info[axisName];\n      const { length, position } = SCROLL_KEYS[axisName];\n      axis.current = this.container[`scroll${position}`];\n      axis.scrollLength = this.container[`scroll${length}`] - this.container[`client${length}`];\n      axis.progress = progress(0, axis.scrollLength, axis.current);\n    };\n    this.update = () => {\n      this.updateAxis(\"x\");\n      this.updateAxis(\"y\");\n    };\n    this.sendEvent = () => {\n      this.callback(this.info);\n    };\n    this.advance = () => {\n      this.update();\n      this.sendEvent();\n    };\n    this.callback = callback;\n    this.container = container;\n    this.info = {\n      time: 0,\n      x: this.createAxis(),\n      y: this.createAxis()\n    };\n  }\n};\n\n// src/dom-events/scroll/index.ts\nvar scrollListeners = /* @__PURE__ */ new WeakMap();\nvar resizeListeners = /* @__PURE__ */ new WeakMap();\nvar onScrollHandlers = /* @__PURE__ */ new WeakMap();\nvar getTarget = (container) => container === document.documentElement ? window : container;\nvar onScroll = (callback, { container = document.documentElement } = {}) => {\n  let containerHandlers = onScrollHandlers.get(container);\n  if (!containerHandlers) {\n    containerHandlers = /* @__PURE__ */ new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  const containerHandler = new ScrollHandler(callback, container);\n  containerHandlers.add(containerHandler);\n  if (!scrollListeners.has(container)) {\n    const listener = () => {\n      containerHandlers?.forEach((handler) => handler.advance());\n      return true;\n    };\n    scrollListeners.set(container, listener);\n    const target = getTarget(container);\n    window.addEventListener(\"resize\", listener, { passive: true });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, onResize(listener, { container }));\n    }\n    target.addEventListener(\"scroll\", listener, { passive: true });\n  }\n  const animateScroll = scrollListeners.get(container);\n  (0,_react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf)(animateScroll);\n  return () => {\n    _react_spring_rafz__WEBPACK_IMPORTED_MODULE_0__.raf.cancel(animateScroll);\n    const containerHandlers2 = onScrollHandlers.get(container);\n    if (!containerHandlers2) return;\n    containerHandlers2.delete(containerHandler);\n    if (containerHandlers2.size) return;\n    const listener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (listener) {\n      getTarget(container).removeEventListener(\"scroll\", listener);\n      window.removeEventListener(\"resize\", listener);\n      resizeListeners.get(container)?.();\n    }\n  };\n};\n\n// src/hooks/useConstant.ts\n\nfunction useConstant(init) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  if (ref.current === null) {\n    ref.current = init();\n  }\n  return ref.current;\n}\n\n// src/hooks/useForceUpdate.ts\n\n\n// src/hooks/useIsMounted.ts\n\n\n// src/hooks/useIsomorphicLayoutEffect.ts\n\nvar useIsomorphicLayoutEffect = isSSR() ? react__WEBPACK_IMPORTED_MODULE_1__.useEffect : react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\n\n// src/hooks/useIsMounted.ts\nvar useIsMounted = () => {\n  const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n  useIsomorphicLayoutEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  return isMounted;\n};\n\n// src/hooks/useForceUpdate.ts\nfunction useForceUpdate() {\n  const update = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)()[1];\n  const isMounted = useIsMounted();\n  return () => {\n    if (isMounted.current) {\n      update(Math.random());\n    }\n  };\n}\n\n// src/hooks/useMemoOne.ts\n\nfunction useMemoOne(getResult, inputs) {\n  const [initial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\n    () => ({\n      inputs,\n      result: getResult()\n    })\n  );\n  const committed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(void 0);\n  const prevCache = committed.current;\n  let cache = prevCache;\n  if (cache) {\n    const useCache = Boolean(\n      inputs && cache.inputs && areInputsEqual(inputs, cache.inputs)\n    );\n    if (!useCache) {\n      cache = {\n        inputs,\n        result: getResult()\n      };\n    }\n  } else {\n    cache = initial;\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    committed.current = cache;\n    if (prevCache == initial) {\n      initial.inputs = initial.result = void 0;\n    }\n  }, [cache]);\n  return cache.result;\n}\nfunction areInputsEqual(next, prev) {\n  if (next.length !== prev.length) {\n    return false;\n  }\n  for (let i = 0; i < next.length; i++) {\n    if (next[i] !== prev[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// src/hooks/useOnce.ts\n\nvar useOnce = (effect) => (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(effect, emptyDeps);\nvar emptyDeps = [];\n\n// src/hooks/usePrev.ts\n\nfunction usePrev(value) {\n  const prevRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(void 0);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    prevRef.current = value;\n  });\n  return prevRef.current;\n}\n\n// src/hooks/useReducedMotion.ts\n\nvar useReducedMotion = () => {\n  const [reducedMotion, setReducedMotion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n  useIsomorphicLayoutEffect(() => {\n    const mql = window.matchMedia(\"(prefers-reduced-motion)\");\n    const handleMediaChange = (e) => {\n      setReducedMotion(e.matches);\n      assign({\n        skipAnimation: e.matches\n      });\n    };\n    handleMediaChange(mql);\n    if (mql.addEventListener) {\n      mql.addEventListener(\"change\", handleMediaChange);\n    } else {\n      mql.addListener(handleMediaChange);\n    }\n    return () => {\n      if (mql.removeEventListener) {\n        mql.removeEventListener(\"change\", handleMediaChange);\n      } else {\n        mql.removeListener(handleMediaChange);\n      }\n    };\n  }, []);\n  return reducedMotion;\n};\n\n// src/index.ts\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXNwcmluZy9zaGFyZWQvZGlzdC9yZWFjdC1zcHJpbmdfc2hhcmVkLm1vZGVybi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixrQ0FBa0M7QUFDaEU7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUN3Qzs7QUFFekM7QUFDQTtBQUNBO0FBQ0EsMEVBQTBFLDJDQUEyQztBQUNySDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGNBQWM7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGdCQUFnQjtBQUNwQyw4QkFBOEIsRUFBRTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLG1EQUFHO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLG1EQUFHO0FBQ3hDLDhCQUE4QixtREFBRztBQUNqQztBQUNBLHlCQUF5QixtREFBRztBQUM1Qjs7QUFFQTtBQUNpRDtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sbURBQUk7QUFDVixNQUFNO0FBQ047QUFDQSxNQUFNLHVEQUFJO0FBQ1Y7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sbURBQUk7QUFDVixNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSx1REFBSTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHlCQUF5QjtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixFQUFFLGNBQWMsRUFBRSxjQUFjLEVBQUU7QUFDN0QsMkJBQTJCLEVBQUUsY0FBYyxFQUFFLGNBQWMsRUFBRSxjQUFjLEVBQUU7QUFDN0UsMkJBQTJCLEVBQUU7QUFDN0IsMkJBQTJCLEVBQUU7O0FBRTdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsRUFBRSxJQUFJLEVBQUUsSUFBSSxFQUFFLElBQUksRUFBRTtBQUNyQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLDJCQUEyQjtBQUM3QztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQ7QUFDQTtBQUNBLGdDQUFnQyxFQUFFLEVBQUUsSUFBSSxZQUFZLEVBQUUsK0JBQStCLElBQUk7QUFDekYsK0JBQStCLG1CQUFtQjtBQUNsRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsK0NBQStDLGVBQWUsSUFBSSxlQUFlLElBQUksZUFBZSxJQUFJLEdBQUc7QUFDM0c7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsOEJBQThCO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0Esc0NBQXNDLDRCQUE0QjtBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLDBCQUEwQixFQUFFLGtCQUFrQjtBQUM3RDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLE9BQU87QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU8sT0FBTztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLE9BQU87QUFDZDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLG9FQUFvRTtBQUNwRTs7QUFFQTtBQUNpRDs7QUFFakQ7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELHFCQUFxQjtBQUM3RTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSw0QkFBNEIsdUNBQXVDLElBQUk7QUFDdkU7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGNBQWMsbUJBQW1CO0FBQ2pDLDZDQUE2QyxTQUFTO0FBQ3RELGtEQUFrRCxPQUFPLDZCQUE2QixPQUFPO0FBQzdGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsdUNBQXVDLElBQUk7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxlQUFlO0FBQ2pFO0FBQ0EsMERBQTBELFdBQVc7QUFDckU7QUFDQSxrREFBa0QsZUFBZTtBQUNqRTtBQUNBO0FBQ0EsRUFBRSx1REFBSTtBQUNOO0FBQ0EsSUFBSSxtREFBSTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQytCO0FBQy9CO0FBQ0EsY0FBYyw2Q0FBTTtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ2lDOztBQUVqQztBQUMwQzs7QUFFMUM7QUFDbUQ7QUFDbkQsMENBQTBDLDRDQUFTLEdBQUcsa0RBQWU7O0FBRXJFO0FBQ0E7QUFDQSxvQkFBb0IsNkNBQU87QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0E7QUFDQSxpQkFBaUIsK0NBQVE7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDMEY7QUFDMUY7QUFDQSxvQkFBb0IsK0NBQVM7QUFDN0I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0Esb0JBQW9CLDZDQUFPO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsRUFBRSxnREFBVTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixpQkFBaUI7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ2dEO0FBQ2hELDBCQUEwQixnREFBVTtBQUNwQzs7QUFFQTtBQUNtRTtBQUNuRTtBQUNBLGtCQUFrQiw2Q0FBTztBQUN6QixFQUFFLGdEQUFVO0FBQ1o7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUM4QztBQUM5QztBQUNBLDRDQUE0QywrQ0FBUztBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNpRDtBQW9EL0MiLCJzb3VyY2VzIjpbIkQ6XFxpZGxlc2NhcGVyXFx2aWtyYW0tZ2FtZVxcbm9kZV9tb2R1bGVzXFxAcmVhY3Qtc3ByaW5nXFxzaGFyZWRcXGRpc3RcXHJlYWN0LXNwcmluZ19zaGFyZWQubW9kZXJuLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgX19kZWZQcm9wID0gT2JqZWN0LmRlZmluZVByb3BlcnR5O1xudmFyIF9fZXhwb3J0ID0gKHRhcmdldCwgYWxsKSA9PiB7XG4gIGZvciAodmFyIG5hbWUgaW4gYWxsKVxuICAgIF9fZGVmUHJvcCh0YXJnZXQsIG5hbWUsIHsgZ2V0OiBhbGxbbmFtZV0sIGVudW1lcmFibGU6IHRydWUgfSk7XG59O1xuXG4vLyBzcmMvZ2xvYmFscy50c1xudmFyIGdsb2JhbHNfZXhwb3J0cyA9IHt9O1xuX19leHBvcnQoZ2xvYmFsc19leHBvcnRzLCB7XG4gIGFzc2lnbjogKCkgPT4gYXNzaWduLFxuICBjb2xvcnM6ICgpID0+IGNvbG9ycyxcbiAgY3JlYXRlU3RyaW5nSW50ZXJwb2xhdG9yOiAoKSA9PiBjcmVhdGVTdHJpbmdJbnRlcnBvbGF0b3IsXG4gIHNraXBBbmltYXRpb246ICgpID0+IHNraXBBbmltYXRpb24sXG4gIHRvOiAoKSA9PiB0byxcbiAgd2lsbEFkdmFuY2U6ICgpID0+IHdpbGxBZHZhbmNlXG59KTtcbmltcG9ydCB7IHJhZiB9IGZyb20gXCJAcmVhY3Qtc3ByaW5nL3JhZnpcIjtcblxuLy8gc3JjL2hlbHBlcnMudHNcbmZ1bmN0aW9uIG5vb3AoKSB7XG59XG52YXIgZGVmaW5lSGlkZGVuID0gKG9iaiwga2V5LCB2YWx1ZSkgPT4gT2JqZWN0LmRlZmluZVByb3BlcnR5KG9iaiwga2V5LCB7IHZhbHVlLCB3cml0YWJsZTogdHJ1ZSwgY29uZmlndXJhYmxlOiB0cnVlIH0pO1xudmFyIGlzID0ge1xuICBhcnI6IEFycmF5LmlzQXJyYXksXG4gIG9iajogKGEpID0+ICEhYSAmJiBhLmNvbnN0cnVjdG9yLm5hbWUgPT09IFwiT2JqZWN0XCIsXG4gIGZ1bjogKGEpID0+IHR5cGVvZiBhID09PSBcImZ1bmN0aW9uXCIsXG4gIHN0cjogKGEpID0+IHR5cGVvZiBhID09PSBcInN0cmluZ1wiLFxuICBudW06IChhKSA9PiB0eXBlb2YgYSA9PT0gXCJudW1iZXJcIixcbiAgdW5kOiAoYSkgPT4gYSA9PT0gdm9pZCAwXG59O1xuZnVuY3Rpb24gaXNFcXVhbChhLCBiKSB7XG4gIGlmIChpcy5hcnIoYSkpIHtcbiAgICBpZiAoIWlzLmFycihiKSB8fCBhLmxlbmd0aCAhPT0gYi5sZW5ndGgpIHJldHVybiBmYWxzZTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGEubGVuZ3RoOyBpKyspIHtcbiAgICAgIGlmIChhW2ldICE9PSBiW2ldKSByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xuICB9XG4gIHJldHVybiBhID09PSBiO1xufVxudmFyIGVhY2ggPSAob2JqLCBmbikgPT4gb2JqLmZvckVhY2goZm4pO1xuZnVuY3Rpb24gZWFjaFByb3Aob2JqLCBmbiwgY3R4KSB7XG4gIGlmIChpcy5hcnIob2JqKSkge1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgb2JqLmxlbmd0aDsgaSsrKSB7XG4gICAgICBmbi5jYWxsKGN0eCwgb2JqW2ldLCBgJHtpfWApO1xuICAgIH1cbiAgICByZXR1cm47XG4gIH1cbiAgZm9yIChjb25zdCBrZXkgaW4gb2JqKSB7XG4gICAgaWYgKG9iai5oYXNPd25Qcm9wZXJ0eShrZXkpKSB7XG4gICAgICBmbi5jYWxsKGN0eCwgb2JqW2tleV0sIGtleSk7XG4gICAgfVxuICB9XG59XG52YXIgdG9BcnJheSA9IChhKSA9PiBpcy51bmQoYSkgPyBbXSA6IGlzLmFycihhKSA/IGEgOiBbYV07XG5mdW5jdGlvbiBmbHVzaChxdWV1ZSwgaXRlcmF0b3IpIHtcbiAgaWYgKHF1ZXVlLnNpemUpIHtcbiAgICBjb25zdCBpdGVtcyA9IEFycmF5LmZyb20ocXVldWUpO1xuICAgIHF1ZXVlLmNsZWFyKCk7XG4gICAgZWFjaChpdGVtcywgaXRlcmF0b3IpO1xuICB9XG59XG52YXIgZmx1c2hDYWxscyA9IChxdWV1ZSwgLi4uYXJncykgPT4gZmx1c2gocXVldWUsIChmbikgPT4gZm4oLi4uYXJncykpO1xudmFyIGlzU1NSID0gKCkgPT4gdHlwZW9mIHdpbmRvdyA9PT0gXCJ1bmRlZmluZWRcIiB8fCAhd2luZG93Lm5hdmlnYXRvciB8fCAvU2VydmVyU2lkZVJlbmRlcmluZ3xeRGVub1xcLy8udGVzdCh3aW5kb3cubmF2aWdhdG9yLnVzZXJBZ2VudCk7XG5cbi8vIHNyYy9nbG9iYWxzLnRzXG52YXIgY3JlYXRlU3RyaW5nSW50ZXJwb2xhdG9yO1xudmFyIHRvO1xudmFyIGNvbG9ycyA9IG51bGw7XG52YXIgc2tpcEFuaW1hdGlvbiA9IGZhbHNlO1xudmFyIHdpbGxBZHZhbmNlID0gbm9vcDtcbnZhciBhc3NpZ24gPSAoZ2xvYmFscykgPT4ge1xuICBpZiAoZ2xvYmFscy50bykgdG8gPSBnbG9iYWxzLnRvO1xuICBpZiAoZ2xvYmFscy5ub3cpIHJhZi5ub3cgPSBnbG9iYWxzLm5vdztcbiAgaWYgKGdsb2JhbHMuY29sb3JzICE9PSB2b2lkIDApIGNvbG9ycyA9IGdsb2JhbHMuY29sb3JzO1xuICBpZiAoZ2xvYmFscy5za2lwQW5pbWF0aW9uICE9IG51bGwpIHNraXBBbmltYXRpb24gPSBnbG9iYWxzLnNraXBBbmltYXRpb247XG4gIGlmIChnbG9iYWxzLmNyZWF0ZVN0cmluZ0ludGVycG9sYXRvcilcbiAgICBjcmVhdGVTdHJpbmdJbnRlcnBvbGF0b3IgPSBnbG9iYWxzLmNyZWF0ZVN0cmluZ0ludGVycG9sYXRvcjtcbiAgaWYgKGdsb2JhbHMucmVxdWVzdEFuaW1hdGlvbkZyYW1lKSByYWYudXNlKGdsb2JhbHMucmVxdWVzdEFuaW1hdGlvbkZyYW1lKTtcbiAgaWYgKGdsb2JhbHMuYmF0Y2hlZFVwZGF0ZXMpIHJhZi5iYXRjaGVkVXBkYXRlcyA9IGdsb2JhbHMuYmF0Y2hlZFVwZGF0ZXM7XG4gIGlmIChnbG9iYWxzLndpbGxBZHZhbmNlKSB3aWxsQWR2YW5jZSA9IGdsb2JhbHMud2lsbEFkdmFuY2U7XG4gIGlmIChnbG9iYWxzLmZyYW1lTG9vcCkgcmFmLmZyYW1lTG9vcCA9IGdsb2JhbHMuZnJhbWVMb29wO1xufTtcblxuLy8gc3JjL0ZyYW1lTG9vcC50c1xuaW1wb3J0IHsgcmFmIGFzIHJhZjIgfSBmcm9tIFwiQHJlYWN0LXNwcmluZy9yYWZ6XCI7XG52YXIgc3RhcnRRdWV1ZSA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KCk7XG52YXIgY3VycmVudEZyYW1lID0gW107XG52YXIgcHJldkZyYW1lID0gW107XG52YXIgcHJpb3JpdHkgPSAwO1xudmFyIGZyYW1lTG9vcCA9IHtcbiAgZ2V0IGlkbGUoKSB7XG4gICAgcmV0dXJuICFzdGFydFF1ZXVlLnNpemUgJiYgIWN1cnJlbnRGcmFtZS5sZW5ndGg7XG4gIH0sXG4gIC8qKiBBZHZhbmNlIHRoZSBnaXZlbiBhbmltYXRpb24gb24gZXZlcnkgZnJhbWUgdW50aWwgaWRsZS4gKi9cbiAgc3RhcnQoYW5pbWF0aW9uKSB7XG4gICAgaWYgKHByaW9yaXR5ID4gYW5pbWF0aW9uLnByaW9yaXR5KSB7XG4gICAgICBzdGFydFF1ZXVlLmFkZChhbmltYXRpb24pO1xuICAgICAgcmFmMi5vblN0YXJ0KGZsdXNoU3RhcnRRdWV1ZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHN0YXJ0U2FmZWx5KGFuaW1hdGlvbik7XG4gICAgICByYWYyKGFkdmFuY2UpO1xuICAgIH1cbiAgfSxcbiAgLyoqIEFkdmFuY2UgYWxsIGFuaW1hdGlvbnMgYnkgdGhlIGdpdmVuIHRpbWUuICovXG4gIGFkdmFuY2UsXG4gIC8qKiBDYWxsIHRoaXMgd2hlbiBhbiBhbmltYXRpb24ncyBwcmlvcml0eSBjaGFuZ2VzLiAqL1xuICBzb3J0KGFuaW1hdGlvbikge1xuICAgIGlmIChwcmlvcml0eSkge1xuICAgICAgcmFmMi5vbkZyYW1lKCgpID0+IGZyYW1lTG9vcC5zb3J0KGFuaW1hdGlvbikpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCBwcmV2SW5kZXggPSBjdXJyZW50RnJhbWUuaW5kZXhPZihhbmltYXRpb24pO1xuICAgICAgaWYgKH5wcmV2SW5kZXgpIHtcbiAgICAgICAgY3VycmVudEZyYW1lLnNwbGljZShwcmV2SW5kZXgsIDEpO1xuICAgICAgICBzdGFydFVuc2FmZWx5KGFuaW1hdGlvbik7XG4gICAgICB9XG4gICAgfVxuICB9LFxuICAvKipcbiAgICogQ2xlYXIgYWxsIGFuaW1hdGlvbnMuIEZvciB0ZXN0aW5nIHB1cnBvc2VzLlxuICAgKlxuICAgKiDimKDvuI8gTmV2ZXIgY2FsbCB0aGlzIGZyb20gd2l0aGluIHRoZSBmcmFtZWxvb3AuXG4gICAqL1xuICBjbGVhcigpIHtcbiAgICBjdXJyZW50RnJhbWUgPSBbXTtcbiAgICBzdGFydFF1ZXVlLmNsZWFyKCk7XG4gIH1cbn07XG5mdW5jdGlvbiBmbHVzaFN0YXJ0UXVldWUoKSB7XG4gIHN0YXJ0UXVldWUuZm9yRWFjaChzdGFydFNhZmVseSk7XG4gIHN0YXJ0UXVldWUuY2xlYXIoKTtcbiAgcmFmMihhZHZhbmNlKTtcbn1cbmZ1bmN0aW9uIHN0YXJ0U2FmZWx5KGFuaW1hdGlvbikge1xuICBpZiAoIWN1cnJlbnRGcmFtZS5pbmNsdWRlcyhhbmltYXRpb24pKSBzdGFydFVuc2FmZWx5KGFuaW1hdGlvbik7XG59XG5mdW5jdGlvbiBzdGFydFVuc2FmZWx5KGFuaW1hdGlvbikge1xuICBjdXJyZW50RnJhbWUuc3BsaWNlKFxuICAgIGZpbmRJbmRleChjdXJyZW50RnJhbWUsIChvdGhlcikgPT4gb3RoZXIucHJpb3JpdHkgPiBhbmltYXRpb24ucHJpb3JpdHkpLFxuICAgIDAsXG4gICAgYW5pbWF0aW9uXG4gICk7XG59XG5mdW5jdGlvbiBhZHZhbmNlKGR0KSB7XG4gIGNvbnN0IG5leHRGcmFtZSA9IHByZXZGcmFtZTtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBjdXJyZW50RnJhbWUubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCBhbmltYXRpb24gPSBjdXJyZW50RnJhbWVbaV07XG4gICAgcHJpb3JpdHkgPSBhbmltYXRpb24ucHJpb3JpdHk7XG4gICAgaWYgKCFhbmltYXRpb24uaWRsZSkge1xuICAgICAgd2lsbEFkdmFuY2UoYW5pbWF0aW9uKTtcbiAgICAgIGFuaW1hdGlvbi5hZHZhbmNlKGR0KTtcbiAgICAgIGlmICghYW5pbWF0aW9uLmlkbGUpIHtcbiAgICAgICAgbmV4dEZyYW1lLnB1c2goYW5pbWF0aW9uKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcHJpb3JpdHkgPSAwO1xuICBwcmV2RnJhbWUgPSBjdXJyZW50RnJhbWU7XG4gIHByZXZGcmFtZS5sZW5ndGggPSAwO1xuICBjdXJyZW50RnJhbWUgPSBuZXh0RnJhbWU7XG4gIHJldHVybiBjdXJyZW50RnJhbWUubGVuZ3RoID4gMDtcbn1cbmZ1bmN0aW9uIGZpbmRJbmRleChhcnIsIHRlc3QpIHtcbiAgY29uc3QgaW5kZXggPSBhcnIuZmluZEluZGV4KHRlc3QpO1xuICByZXR1cm4gaW5kZXggPCAwID8gYXJyLmxlbmd0aCA6IGluZGV4O1xufVxuXG4vLyBzcmMvY2xhbXAudHNcbnZhciBjbGFtcCA9IChtaW4sIG1heCwgdikgPT4gTWF0aC5taW4oTWF0aC5tYXgodiwgbWluKSwgbWF4KTtcblxuLy8gc3JjL2NvbG9ycy50c1xudmFyIGNvbG9yczIgPSB7XG4gIHRyYW5zcGFyZW50OiAwLFxuICBhbGljZWJsdWU6IDQwNDI4NTAzMDMsXG4gIGFudGlxdWV3aGl0ZTogNDIwOTc2MDI1NSxcbiAgYXF1YTogMTY3NzcyMTUsXG4gIGFxdWFtYXJpbmU6IDIxNDc0NzI2MzksXG4gIGF6dXJlOiA0MDQzMzA5MDU1LFxuICBiZWlnZTogNDEyNjUzMDgxNSxcbiAgYmlzcXVlOiA0MjkzMTgyNzE5LFxuICBibGFjazogMjU1LFxuICBibGFuY2hlZGFsbW9uZDogNDI5MzY0Mzc3NSxcbiAgYmx1ZTogNjU1MzUsXG4gIGJsdWV2aW9sZXQ6IDIzMTgxMzE5NjcsXG4gIGJyb3duOiAyNzcxMDA0MTU5LFxuICBidXJseXdvb2Q6IDM3MzY2MzUzOTEsXG4gIGJ1cm50c2llbm5hOiAzOTM0MTUwMTQzLFxuICBjYWRldGJsdWU6IDE2MDQyMzE0MjMsXG4gIGNoYXJ0cmV1c2U6IDIxNDc0MTgzNjcsXG4gIGNob2NvbGF0ZTogMzUzMDEwNDU3NSxcbiAgY29yYWw6IDQyODY1MzM4ODcsXG4gIGNvcm5mbG93ZXJibHVlOiAxNjg3NTQ3MzkxLFxuICBjb3Juc2lsazogNDI5NDQ5OTU4MyxcbiAgY3JpbXNvbjogMzY5MjMxMzg1NSxcbiAgY3lhbjogMTY3NzcyMTUsXG4gIGRhcmtibHVlOiAzNTgzOSxcbiAgZGFya2N5YW46IDkxNDUzNDMsXG4gIGRhcmtnb2xkZW5yb2Q6IDMwOTU3OTI2MzksXG4gIGRhcmtncmF5OiAyODQ2NDY4NjA3LFxuICBkYXJrZ3JlZW46IDY1NTM4NTUsXG4gIGRhcmtncmV5OiAyODQ2NDY4NjA3LFxuICBkYXJra2hha2k6IDMxODI5MTQ1NTksXG4gIGRhcmttYWdlbnRhOiAyMzMyMDY4ODYzLFxuICBkYXJrb2xpdmVncmVlbjogMTQzMzA4Nzk5OSxcbiAgZGFya29yYW5nZTogNDI4NzM2NTM3NSxcbiAgZGFya29yY2hpZDogMjU3MDI0MzMyNyxcbiAgZGFya3JlZDogMjMzMjAzMzI3OSxcbiAgZGFya3NhbG1vbjogMzkxODk1MzIxNSxcbiAgZGFya3NlYWdyZWVuOiAyNDExNDk5NTE5LFxuICBkYXJrc2xhdGVibHVlOiAxMjExOTkzMDg3LFxuICBkYXJrc2xhdGVncmF5OiA3OTM3MjY5NzUsXG4gIGRhcmtzbGF0ZWdyZXk6IDc5MzcyNjk3NSxcbiAgZGFya3R1cnF1b2lzZTogMTM1NTQxNzUsXG4gIGRhcmt2aW9sZXQ6IDI0ODMwODIyMzksXG4gIGRlZXBwaW5rOiA0Mjc5NTM4Njg3LFxuICBkZWVwc2t5Ymx1ZTogMTI1ODI5MTEsXG4gIGRpbWdyYXk6IDE3Njg1MTYwOTUsXG4gIGRpbWdyZXk6IDE3Njg1MTYwOTUsXG4gIGRvZGdlcmJsdWU6IDUxMjgxOTE5OSxcbiAgZmlyZWJyaWNrOiAyOTg4NTgxNjMxLFxuICBmbG9yYWx3aGl0ZTogNDI5NDYzNTc3NSxcbiAgZm9yZXN0Z3JlZW46IDU3OTU0MzgwNyxcbiAgZnVjaHNpYTogNDI3ODI1NTYxNSxcbiAgZ2FpbnNib3JvOiAzNzA1NDYyMDE1LFxuICBnaG9zdHdoaXRlOiA0MTc3MDY4MDMxLFxuICBnb2xkOiA0MjkyMjgwNTc1LFxuICBnb2xkZW5yb2Q6IDM2NjgyNTQ5NzUsXG4gIGdyYXk6IDIxNTU5MDUyNzksXG4gIGdyZWVuOiA4Mzg4ODYzLFxuICBncmVlbnllbGxvdzogMjkxOTE4MjMzNSxcbiAgZ3JleTogMjE1NTkwNTI3OSxcbiAgaG9uZXlkZXc6IDQwNDMzMDUyMTUsXG4gIGhvdHBpbms6IDQyODUxMTc2OTUsXG4gIGluZGlhbnJlZDogMzQ0NTM4MjM5OSxcbiAgaW5kaWdvOiAxMjU4MzI0NzM1LFxuICBpdm9yeTogNDI5NDk2MzQ1NSxcbiAga2hha2k6IDQwNDE2NDEyMTUsXG4gIGxhdmVuZGVyOiAzODczODk3MjE1LFxuICBsYXZlbmRlcmJsdXNoOiA0MjkzOTgxNjk1LFxuICBsYXduZ3JlZW46IDIwOTY4OTAxMTEsXG4gIGxlbW9uY2hpZmZvbjogNDI5NDYyNjgxNSxcbiAgbGlnaHRibHVlOiAyOTE2NjczMjc5LFxuICBsaWdodGNvcmFsOiA0MDM0OTUzNDcxLFxuICBsaWdodGN5YW46IDM3NzQ4NzM1OTksXG4gIGxpZ2h0Z29sZGVucm9keWVsbG93OiA0MjEwNzQyMDE1LFxuICBsaWdodGdyYXk6IDM1NTM4NzQ5NDMsXG4gIGxpZ2h0Z3JlZW46IDI0MzE1NTM3OTEsXG4gIGxpZ2h0Z3JleTogMzU1Mzg3NDk0MyxcbiAgbGlnaHRwaW5rOiA0MjkwMTY3Mjk1LFxuICBsaWdodHNhbG1vbjogNDI4ODcwNzMyNyxcbiAgbGlnaHRzZWFncmVlbjogNTQ4NTgwMDk1LFxuICBsaWdodHNreWJsdWU6IDIyNzg0ODg4MzEsXG4gIGxpZ2h0c2xhdGVncmF5OiAyMDA1NDQxMDIzLFxuICBsaWdodHNsYXRlZ3JleTogMjAwNTQ0MTAyMyxcbiAgbGlnaHRzdGVlbGJsdWU6IDI5NjU2OTIxNTksXG4gIGxpZ2h0eWVsbG93OiA0Mjk0OTU5MzU5LFxuICBsaW1lOiAxNjcxMTkzNSxcbiAgbGltZWdyZWVuOiA4NTIzMDg3MzUsXG4gIGxpbmVuOiA0MjEwMDkxNzc1LFxuICBtYWdlbnRhOiA0Mjc4MjU1NjE1LFxuICBtYXJvb246IDIxNDc0ODM5MDMsXG4gIG1lZGl1bWFxdWFtYXJpbmU6IDE3MjQ3NTQ2ODcsXG4gIG1lZGl1bWJsdWU6IDUyNzM1LFxuICBtZWRpdW1vcmNoaWQ6IDMxMjYxODcwMDcsXG4gIG1lZGl1bXB1cnBsZTogMjQ3MzY0NzEwMyxcbiAgbWVkaXVtc2VhZ3JlZW46IDEwMTgzOTMwODcsXG4gIG1lZGl1bXNsYXRlYmx1ZTogMjA3MDQ3NDQ5NSxcbiAgbWVkaXVtc3ByaW5nZ3JlZW46IDE2NDIzNjc5LFxuICBtZWRpdW10dXJxdW9pc2U6IDEyMjE3MDkwNTUsXG4gIG1lZGl1bXZpb2xldHJlZDogMzM0MDA3NjU0MyxcbiAgbWlkbmlnaHRibHVlOiA0MjEwOTc3MjcsXG4gIG1pbnRjcmVhbTogNDEyNzE5Mzg1NSxcbiAgbWlzdHlyb3NlOiA0MjkzMTkwMTQzLFxuICBtb2NjYXNpbjogNDI5MzE3ODg3OSxcbiAgbmF2YWpvd2hpdGU6IDQyOTI3ODM2MTUsXG4gIG5hdnk6IDMzMDIzLFxuICBvbGRsYWNlOiA0MjYwNzUxMTAzLFxuICBvbGl2ZTogMjE1NTg3MjUxMSxcbiAgb2xpdmVkcmFiOiAxODA0NDc3NDM5LFxuICBvcmFuZ2U6IDQyODkwMDM3NzUsXG4gIG9yYW5nZXJlZDogNDI4MjcxMjMxOSxcbiAgb3JjaGlkOiAzNjY0ODI4MTU5LFxuICBwYWxlZ29sZGVucm9kOiA0MDA4MjI1NTM1LFxuICBwYWxlZ3JlZW46IDI1NjY2MjU1MzUsXG4gIHBhbGV0dXJxdW9pc2U6IDI5NTE2NzE1NTEsXG4gIHBhbGV2aW9sZXRyZWQ6IDM2ODE1ODgyMjMsXG4gIHBhcGF5YXdoaXA6IDQyOTM5MDc5NjcsXG4gIHBlYWNocHVmZjogNDI5MjUyNDU0MyxcbiAgcGVydTogMzQ0ODA2MTk1MSxcbiAgcGluazogNDI5MDgyNTIxNSxcbiAgcGx1bTogMzcxODMwNzMyNyxcbiAgcG93ZGVyYmx1ZTogMjk2NzUyOTIxNSxcbiAgcHVycGxlOiAyMTQ3NTE2NjcxLFxuICByZWJlY2NhcHVycGxlOiAxNzE0NjU3NzkxLFxuICByZWQ6IDQyNzgxOTAzMzUsXG4gIHJvc3licm93bjogMzE2MzUyNTExOSxcbiAgcm95YWxibHVlOiAxMDk3NDU4MTc1LFxuICBzYWRkbGVicm93bjogMjMzNjU2MDEyNyxcbiAgc2FsbW9uOiA0MjAyNzIyMDQ3LFxuICBzYW5keWJyb3duOiA0MTA0NDEzNDM5LFxuICBzZWFncmVlbjogNzgwODgzOTY3LFxuICBzZWFzaGVsbDogNDI5NDMwNzU4MyxcbiAgc2llbm5hOiAyNjg5NzQwMjg3LFxuICBzaWx2ZXI6IDMyMzM4NTc3OTEsXG4gIHNreWJsdWU6IDIyNzg0ODQ5OTEsXG4gIHNsYXRlYmx1ZTogMTc4NDMzNTg3MSxcbiAgc2xhdGVncmF5OiAxODg3NDczOTE5LFxuICBzbGF0ZWdyZXk6IDE4ODc0NzM5MTksXG4gIHNub3c6IDQyOTQ2MzgzMzUsXG4gIHNwcmluZ2dyZWVuOiAxNjc0NDQ0NyxcbiAgc3RlZWxibHVlOiAxMTgyOTcxMTM1LFxuICB0YW46IDM1MzUwNDc5MzUsXG4gIHRlYWw6IDg0MjE2MzEsXG4gIHRoaXN0bGU6IDM2MzY0NTE1ODMsXG4gIHRvbWF0bzogNDI4NDY5NjU3NSxcbiAgdHVycXVvaXNlOiAxMDg4NDc1MzkxLFxuICB2aW9sZXQ6IDQwMDE1NTgyNzEsXG4gIHdoZWF0OiA0MTI1MDEyOTkxLFxuICB3aGl0ZTogNDI5NDk2NzI5NSxcbiAgd2hpdGVzbW9rZTogNDEyNjUzNzIxNSxcbiAgeWVsbG93OiA0Mjk0OTAyMDE1LFxuICB5ZWxsb3dncmVlbjogMjU5NzEzOTE5OVxufTtcblxuLy8gc3JjL2NvbG9yTWF0Y2hlcnMudHNcbnZhciBOVU1CRVIgPSBcIlstK10/XFxcXGQqXFxcXC4/XFxcXGQrXCI7XG52YXIgUEVSQ0VOVEFHRSA9IE5VTUJFUiArIFwiJVwiO1xuZnVuY3Rpb24gY2FsbCguLi5wYXJ0cykge1xuICByZXR1cm4gXCJcXFxcKFxcXFxzKihcIiArIHBhcnRzLmpvaW4oXCIpXFxcXHMqLFxcXFxzKihcIikgKyBcIilcXFxccypcXFxcKVwiO1xufVxudmFyIHJnYiA9IG5ldyBSZWdFeHAoXCJyZ2JcIiArIGNhbGwoTlVNQkVSLCBOVU1CRVIsIE5VTUJFUikpO1xudmFyIHJnYmEgPSBuZXcgUmVnRXhwKFwicmdiYVwiICsgY2FsbChOVU1CRVIsIE5VTUJFUiwgTlVNQkVSLCBOVU1CRVIpKTtcbnZhciBoc2wgPSBuZXcgUmVnRXhwKFwiaHNsXCIgKyBjYWxsKE5VTUJFUiwgUEVSQ0VOVEFHRSwgUEVSQ0VOVEFHRSkpO1xudmFyIGhzbGEgPSBuZXcgUmVnRXhwKFxuICBcImhzbGFcIiArIGNhbGwoTlVNQkVSLCBQRVJDRU5UQUdFLCBQRVJDRU5UQUdFLCBOVU1CRVIpXG4pO1xudmFyIGhleDMgPSAvXiMoWzAtOWEtZkEtRl17MX0pKFswLTlhLWZBLUZdezF9KShbMC05YS1mQS1GXXsxfSkkLztcbnZhciBoZXg0ID0gL14jKFswLTlhLWZBLUZdezF9KShbMC05YS1mQS1GXXsxfSkoWzAtOWEtZkEtRl17MX0pKFswLTlhLWZBLUZdezF9KSQvO1xudmFyIGhleDYgPSAvXiMoWzAtOWEtZkEtRl17Nn0pJC87XG52YXIgaGV4OCA9IC9eIyhbMC05YS1mQS1GXXs4fSkkLztcblxuLy8gc3JjL25vcm1hbGl6ZUNvbG9yLnRzXG5mdW5jdGlvbiBub3JtYWxpemVDb2xvcihjb2xvcikge1xuICBsZXQgbWF0Y2g7XG4gIGlmICh0eXBlb2YgY29sb3IgPT09IFwibnVtYmVyXCIpIHtcbiAgICByZXR1cm4gY29sb3IgPj4+IDAgPT09IGNvbG9yICYmIGNvbG9yID49IDAgJiYgY29sb3IgPD0gNDI5NDk2NzI5NSA/IGNvbG9yIDogbnVsbDtcbiAgfVxuICBpZiAobWF0Y2ggPSBoZXg2LmV4ZWMoY29sb3IpKVxuICAgIHJldHVybiBwYXJzZUludChtYXRjaFsxXSArIFwiZmZcIiwgMTYpID4+PiAwO1xuICBpZiAoY29sb3JzICYmIGNvbG9yc1tjb2xvcl0gIT09IHZvaWQgMCkge1xuICAgIHJldHVybiBjb2xvcnNbY29sb3JdO1xuICB9XG4gIGlmIChtYXRjaCA9IHJnYi5leGVjKGNvbG9yKSkge1xuICAgIHJldHVybiAocGFyc2UyNTUobWF0Y2hbMV0pIDw8IDI0IHwgLy8gclxuICAgIHBhcnNlMjU1KG1hdGNoWzJdKSA8PCAxNiB8IC8vIGdcbiAgICBwYXJzZTI1NShtYXRjaFszXSkgPDwgOCB8IC8vIGJcbiAgICAyNTUpID4+PiAvLyBhXG4gICAgMDtcbiAgfVxuICBpZiAobWF0Y2ggPSByZ2JhLmV4ZWMoY29sb3IpKSB7XG4gICAgcmV0dXJuIChwYXJzZTI1NShtYXRjaFsxXSkgPDwgMjQgfCAvLyByXG4gICAgcGFyc2UyNTUobWF0Y2hbMl0pIDw8IDE2IHwgLy8gZ1xuICAgIHBhcnNlMjU1KG1hdGNoWzNdKSA8PCA4IHwgLy8gYlxuICAgIHBhcnNlMShtYXRjaFs0XSkpID4+PiAvLyBhXG4gICAgMDtcbiAgfVxuICBpZiAobWF0Y2ggPSBoZXgzLmV4ZWMoY29sb3IpKSB7XG4gICAgcmV0dXJuIHBhcnNlSW50KFxuICAgICAgbWF0Y2hbMV0gKyBtYXRjaFsxXSArIC8vIHJcbiAgICAgIG1hdGNoWzJdICsgbWF0Y2hbMl0gKyAvLyBnXG4gICAgICBtYXRjaFszXSArIG1hdGNoWzNdICsgLy8gYlxuICAgICAgXCJmZlwiLFxuICAgICAgLy8gYVxuICAgICAgMTZcbiAgICApID4+PiAwO1xuICB9XG4gIGlmIChtYXRjaCA9IGhleDguZXhlYyhjb2xvcikpIHJldHVybiBwYXJzZUludChtYXRjaFsxXSwgMTYpID4+PiAwO1xuICBpZiAobWF0Y2ggPSBoZXg0LmV4ZWMoY29sb3IpKSB7XG4gICAgcmV0dXJuIHBhcnNlSW50KFxuICAgICAgbWF0Y2hbMV0gKyBtYXRjaFsxXSArIC8vIHJcbiAgICAgIG1hdGNoWzJdICsgbWF0Y2hbMl0gKyAvLyBnXG4gICAgICBtYXRjaFszXSArIG1hdGNoWzNdICsgLy8gYlxuICAgICAgbWF0Y2hbNF0gKyBtYXRjaFs0XSxcbiAgICAgIC8vIGFcbiAgICAgIDE2XG4gICAgKSA+Pj4gMDtcbiAgfVxuICBpZiAobWF0Y2ggPSBoc2wuZXhlYyhjb2xvcikpIHtcbiAgICByZXR1cm4gKGhzbFRvUmdiKFxuICAgICAgcGFyc2UzNjAobWF0Y2hbMV0pLFxuICAgICAgLy8gaFxuICAgICAgcGFyc2VQZXJjZW50YWdlKG1hdGNoWzJdKSxcbiAgICAgIC8vIHNcbiAgICAgIHBhcnNlUGVyY2VudGFnZShtYXRjaFszXSlcbiAgICAgIC8vIGxcbiAgICApIHwgMjU1KSA+Pj4gLy8gYVxuICAgIDA7XG4gIH1cbiAgaWYgKG1hdGNoID0gaHNsYS5leGVjKGNvbG9yKSkge1xuICAgIHJldHVybiAoaHNsVG9SZ2IoXG4gICAgICBwYXJzZTM2MChtYXRjaFsxXSksXG4gICAgICAvLyBoXG4gICAgICBwYXJzZVBlcmNlbnRhZ2UobWF0Y2hbMl0pLFxuICAgICAgLy8gc1xuICAgICAgcGFyc2VQZXJjZW50YWdlKG1hdGNoWzNdKVxuICAgICAgLy8gbFxuICAgICkgfCBwYXJzZTEobWF0Y2hbNF0pKSA+Pj4gLy8gYVxuICAgIDA7XG4gIH1cbiAgcmV0dXJuIG51bGw7XG59XG5mdW5jdGlvbiBodWUycmdiKHAsIHEsIHQpIHtcbiAgaWYgKHQgPCAwKSB0ICs9IDE7XG4gIGlmICh0ID4gMSkgdCAtPSAxO1xuICBpZiAodCA8IDEgLyA2KSByZXR1cm4gcCArIChxIC0gcCkgKiA2ICogdDtcbiAgaWYgKHQgPCAxIC8gMikgcmV0dXJuIHE7XG4gIGlmICh0IDwgMiAvIDMpIHJldHVybiBwICsgKHEgLSBwKSAqICgyIC8gMyAtIHQpICogNjtcbiAgcmV0dXJuIHA7XG59XG5mdW5jdGlvbiBoc2xUb1JnYihoLCBzLCBsKSB7XG4gIGNvbnN0IHEgPSBsIDwgMC41ID8gbCAqICgxICsgcykgOiBsICsgcyAtIGwgKiBzO1xuICBjb25zdCBwID0gMiAqIGwgLSBxO1xuICBjb25zdCByID0gaHVlMnJnYihwLCBxLCBoICsgMSAvIDMpO1xuICBjb25zdCBnID0gaHVlMnJnYihwLCBxLCBoKTtcbiAgY29uc3QgYiA9IGh1ZTJyZ2IocCwgcSwgaCAtIDEgLyAzKTtcbiAgcmV0dXJuIE1hdGgucm91bmQociAqIDI1NSkgPDwgMjQgfCBNYXRoLnJvdW5kKGcgKiAyNTUpIDw8IDE2IHwgTWF0aC5yb3VuZChiICogMjU1KSA8PCA4O1xufVxuZnVuY3Rpb24gcGFyc2UyNTUoc3RyKSB7XG4gIGNvbnN0IGludCA9IHBhcnNlSW50KHN0ciwgMTApO1xuICBpZiAoaW50IDwgMCkgcmV0dXJuIDA7XG4gIGlmIChpbnQgPiAyNTUpIHJldHVybiAyNTU7XG4gIHJldHVybiBpbnQ7XG59XG5mdW5jdGlvbiBwYXJzZTM2MChzdHIpIHtcbiAgY29uc3QgaW50ID0gcGFyc2VGbG9hdChzdHIpO1xuICByZXR1cm4gKGludCAlIDM2MCArIDM2MCkgJSAzNjAgLyAzNjA7XG59XG5mdW5jdGlvbiBwYXJzZTEoc3RyKSB7XG4gIGNvbnN0IG51bSA9IHBhcnNlRmxvYXQoc3RyKTtcbiAgaWYgKG51bSA8IDApIHJldHVybiAwO1xuICBpZiAobnVtID4gMSkgcmV0dXJuIDI1NTtcbiAgcmV0dXJuIE1hdGgucm91bmQobnVtICogMjU1KTtcbn1cbmZ1bmN0aW9uIHBhcnNlUGVyY2VudGFnZShzdHIpIHtcbiAgY29uc3QgaW50ID0gcGFyc2VGbG9hdChzdHIpO1xuICBpZiAoaW50IDwgMCkgcmV0dXJuIDA7XG4gIGlmIChpbnQgPiAxMDApIHJldHVybiAxO1xuICByZXR1cm4gaW50IC8gMTAwO1xufVxuXG4vLyBzcmMvY29sb3JUb1JnYmEudHNcbmZ1bmN0aW9uIGNvbG9yVG9SZ2JhKGlucHV0KSB7XG4gIGxldCBpbnQzMkNvbG9yID0gbm9ybWFsaXplQ29sb3IoaW5wdXQpO1xuICBpZiAoaW50MzJDb2xvciA9PT0gbnVsbCkgcmV0dXJuIGlucHV0O1xuICBpbnQzMkNvbG9yID0gaW50MzJDb2xvciB8fCAwO1xuICBjb25zdCByID0gKGludDMyQ29sb3IgJiA0Mjc4MTkwMDgwKSA+Pj4gMjQ7XG4gIGNvbnN0IGcgPSAoaW50MzJDb2xvciAmIDE2NzExNjgwKSA+Pj4gMTY7XG4gIGNvbnN0IGIgPSAoaW50MzJDb2xvciAmIDY1MjgwKSA+Pj4gODtcbiAgY29uc3QgYSA9IChpbnQzMkNvbG9yICYgMjU1KSAvIDI1NTtcbiAgcmV0dXJuIGByZ2JhKCR7cn0sICR7Z30sICR7Yn0sICR7YX0pYDtcbn1cblxuLy8gc3JjL2NyZWF0ZUludGVycG9sYXRvci50c1xudmFyIGNyZWF0ZUludGVycG9sYXRvciA9IChyYW5nZSwgb3V0cHV0LCBleHRyYXBvbGF0ZSkgPT4ge1xuICBpZiAoaXMuZnVuKHJhbmdlKSkge1xuICAgIHJldHVybiByYW5nZTtcbiAgfVxuICBpZiAoaXMuYXJyKHJhbmdlKSkge1xuICAgIHJldHVybiBjcmVhdGVJbnRlcnBvbGF0b3Ioe1xuICAgICAgcmFuZ2UsXG4gICAgICBvdXRwdXQsXG4gICAgICBleHRyYXBvbGF0ZVxuICAgIH0pO1xuICB9XG4gIGlmIChpcy5zdHIocmFuZ2Uub3V0cHV0WzBdKSkge1xuICAgIHJldHVybiBjcmVhdGVTdHJpbmdJbnRlcnBvbGF0b3IocmFuZ2UpO1xuICB9XG4gIGNvbnN0IGNvbmZpZyA9IHJhbmdlO1xuICBjb25zdCBvdXRwdXRSYW5nZSA9IGNvbmZpZy5vdXRwdXQ7XG4gIGNvbnN0IGlucHV0UmFuZ2UgPSBjb25maWcucmFuZ2UgfHwgWzAsIDFdO1xuICBjb25zdCBleHRyYXBvbGF0ZUxlZnQgPSBjb25maWcuZXh0cmFwb2xhdGVMZWZ0IHx8IGNvbmZpZy5leHRyYXBvbGF0ZSB8fCBcImV4dGVuZFwiO1xuICBjb25zdCBleHRyYXBvbGF0ZVJpZ2h0ID0gY29uZmlnLmV4dHJhcG9sYXRlUmlnaHQgfHwgY29uZmlnLmV4dHJhcG9sYXRlIHx8IFwiZXh0ZW5kXCI7XG4gIGNvbnN0IGVhc2luZyA9IGNvbmZpZy5lYXNpbmcgfHwgKCh0KSA9PiB0KTtcbiAgcmV0dXJuIChpbnB1dCkgPT4ge1xuICAgIGNvbnN0IHJhbmdlMiA9IGZpbmRSYW5nZShpbnB1dCwgaW5wdXRSYW5nZSk7XG4gICAgcmV0dXJuIGludGVycG9sYXRlKFxuICAgICAgaW5wdXQsXG4gICAgICBpbnB1dFJhbmdlW3JhbmdlMl0sXG4gICAgICBpbnB1dFJhbmdlW3JhbmdlMiArIDFdLFxuICAgICAgb3V0cHV0UmFuZ2VbcmFuZ2UyXSxcbiAgICAgIG91dHB1dFJhbmdlW3JhbmdlMiArIDFdLFxuICAgICAgZWFzaW5nLFxuICAgICAgZXh0cmFwb2xhdGVMZWZ0LFxuICAgICAgZXh0cmFwb2xhdGVSaWdodCxcbiAgICAgIGNvbmZpZy5tYXBcbiAgICApO1xuICB9O1xufTtcbmZ1bmN0aW9uIGludGVycG9sYXRlKGlucHV0LCBpbnB1dE1pbiwgaW5wdXRNYXgsIG91dHB1dE1pbiwgb3V0cHV0TWF4LCBlYXNpbmcsIGV4dHJhcG9sYXRlTGVmdCwgZXh0cmFwb2xhdGVSaWdodCwgbWFwKSB7XG4gIGxldCByZXN1bHQgPSBtYXAgPyBtYXAoaW5wdXQpIDogaW5wdXQ7XG4gIGlmIChyZXN1bHQgPCBpbnB1dE1pbikge1xuICAgIGlmIChleHRyYXBvbGF0ZUxlZnQgPT09IFwiaWRlbnRpdHlcIikgcmV0dXJuIHJlc3VsdDtcbiAgICBlbHNlIGlmIChleHRyYXBvbGF0ZUxlZnQgPT09IFwiY2xhbXBcIikgcmVzdWx0ID0gaW5wdXRNaW47XG4gIH1cbiAgaWYgKHJlc3VsdCA+IGlucHV0TWF4KSB7XG4gICAgaWYgKGV4dHJhcG9sYXRlUmlnaHQgPT09IFwiaWRlbnRpdHlcIikgcmV0dXJuIHJlc3VsdDtcbiAgICBlbHNlIGlmIChleHRyYXBvbGF0ZVJpZ2h0ID09PSBcImNsYW1wXCIpIHJlc3VsdCA9IGlucHV0TWF4O1xuICB9XG4gIGlmIChvdXRwdXRNaW4gPT09IG91dHB1dE1heCkgcmV0dXJuIG91dHB1dE1pbjtcbiAgaWYgKGlucHV0TWluID09PSBpbnB1dE1heCkgcmV0dXJuIGlucHV0IDw9IGlucHV0TWluID8gb3V0cHV0TWluIDogb3V0cHV0TWF4O1xuICBpZiAoaW5wdXRNaW4gPT09IC1JbmZpbml0eSkgcmVzdWx0ID0gLXJlc3VsdDtcbiAgZWxzZSBpZiAoaW5wdXRNYXggPT09IEluZmluaXR5KSByZXN1bHQgPSByZXN1bHQgLSBpbnB1dE1pbjtcbiAgZWxzZSByZXN1bHQgPSAocmVzdWx0IC0gaW5wdXRNaW4pIC8gKGlucHV0TWF4IC0gaW5wdXRNaW4pO1xuICByZXN1bHQgPSBlYXNpbmcocmVzdWx0KTtcbiAgaWYgKG91dHB1dE1pbiA9PT0gLUluZmluaXR5KSByZXN1bHQgPSAtcmVzdWx0O1xuICBlbHNlIGlmIChvdXRwdXRNYXggPT09IEluZmluaXR5KSByZXN1bHQgPSByZXN1bHQgKyBvdXRwdXRNaW47XG4gIGVsc2UgcmVzdWx0ID0gcmVzdWx0ICogKG91dHB1dE1heCAtIG91dHB1dE1pbikgKyBvdXRwdXRNaW47XG4gIHJldHVybiByZXN1bHQ7XG59XG5mdW5jdGlvbiBmaW5kUmFuZ2UoaW5wdXQsIGlucHV0UmFuZ2UpIHtcbiAgZm9yICh2YXIgaSA9IDE7IGkgPCBpbnB1dFJhbmdlLmxlbmd0aCAtIDE7ICsraSlcbiAgICBpZiAoaW5wdXRSYW5nZVtpXSA+PSBpbnB1dCkgYnJlYWs7XG4gIHJldHVybiBpIC0gMTtcbn1cblxuLy8gc3JjL2Vhc2luZ3MudHNcbnZhciBzdGVwcyA9IChzdGVwczIsIGRpcmVjdGlvbiA9IFwiZW5kXCIpID0+IChwcm9ncmVzczIpID0+IHtcbiAgcHJvZ3Jlc3MyID0gZGlyZWN0aW9uID09PSBcImVuZFwiID8gTWF0aC5taW4ocHJvZ3Jlc3MyLCAwLjk5OSkgOiBNYXRoLm1heChwcm9ncmVzczIsIDFlLTMpO1xuICBjb25zdCBleHBhbmRlZCA9IHByb2dyZXNzMiAqIHN0ZXBzMjtcbiAgY29uc3Qgcm91bmRlZCA9IGRpcmVjdGlvbiA9PT0gXCJlbmRcIiA/IE1hdGguZmxvb3IoZXhwYW5kZWQpIDogTWF0aC5jZWlsKGV4cGFuZGVkKTtcbiAgcmV0dXJuIGNsYW1wKDAsIDEsIHJvdW5kZWQgLyBzdGVwczIpO1xufTtcbnZhciBjMSA9IDEuNzAxNTg7XG52YXIgYzIgPSBjMSAqIDEuNTI1O1xudmFyIGMzID0gYzEgKyAxO1xudmFyIGM0ID0gMiAqIE1hdGguUEkgLyAzO1xudmFyIGM1ID0gMiAqIE1hdGguUEkgLyA0LjU7XG52YXIgYm91bmNlT3V0ID0gKHgpID0+IHtcbiAgY29uc3QgbjEgPSA3LjU2MjU7XG4gIGNvbnN0IGQxID0gMi43NTtcbiAgaWYgKHggPCAxIC8gZDEpIHtcbiAgICByZXR1cm4gbjEgKiB4ICogeDtcbiAgfSBlbHNlIGlmICh4IDwgMiAvIGQxKSB7XG4gICAgcmV0dXJuIG4xICogKHggLT0gMS41IC8gZDEpICogeCArIDAuNzU7XG4gIH0gZWxzZSBpZiAoeCA8IDIuNSAvIGQxKSB7XG4gICAgcmV0dXJuIG4xICogKHggLT0gMi4yNSAvIGQxKSAqIHggKyAwLjkzNzU7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIG4xICogKHggLT0gMi42MjUgLyBkMSkgKiB4ICsgMC45ODQzNzU7XG4gIH1cbn07XG52YXIgZWFzaW5ncyA9IHtcbiAgbGluZWFyOiAoeCkgPT4geCxcbiAgZWFzZUluUXVhZDogKHgpID0+IHggKiB4LFxuICBlYXNlT3V0UXVhZDogKHgpID0+IDEgLSAoMSAtIHgpICogKDEgLSB4KSxcbiAgZWFzZUluT3V0UXVhZDogKHgpID0+IHggPCAwLjUgPyAyICogeCAqIHggOiAxIC0gTWF0aC5wb3coLTIgKiB4ICsgMiwgMikgLyAyLFxuICBlYXNlSW5DdWJpYzogKHgpID0+IHggKiB4ICogeCxcbiAgZWFzZU91dEN1YmljOiAoeCkgPT4gMSAtIE1hdGgucG93KDEgLSB4LCAzKSxcbiAgZWFzZUluT3V0Q3ViaWM6ICh4KSA9PiB4IDwgMC41ID8gNCAqIHggKiB4ICogeCA6IDEgLSBNYXRoLnBvdygtMiAqIHggKyAyLCAzKSAvIDIsXG4gIGVhc2VJblF1YXJ0OiAoeCkgPT4geCAqIHggKiB4ICogeCxcbiAgZWFzZU91dFF1YXJ0OiAoeCkgPT4gMSAtIE1hdGgucG93KDEgLSB4LCA0KSxcbiAgZWFzZUluT3V0UXVhcnQ6ICh4KSA9PiB4IDwgMC41ID8gOCAqIHggKiB4ICogeCAqIHggOiAxIC0gTWF0aC5wb3coLTIgKiB4ICsgMiwgNCkgLyAyLFxuICBlYXNlSW5RdWludDogKHgpID0+IHggKiB4ICogeCAqIHggKiB4LFxuICBlYXNlT3V0UXVpbnQ6ICh4KSA9PiAxIC0gTWF0aC5wb3coMSAtIHgsIDUpLFxuICBlYXNlSW5PdXRRdWludDogKHgpID0+IHggPCAwLjUgPyAxNiAqIHggKiB4ICogeCAqIHggKiB4IDogMSAtIE1hdGgucG93KC0yICogeCArIDIsIDUpIC8gMixcbiAgZWFzZUluU2luZTogKHgpID0+IDEgLSBNYXRoLmNvcyh4ICogTWF0aC5QSSAvIDIpLFxuICBlYXNlT3V0U2luZTogKHgpID0+IE1hdGguc2luKHggKiBNYXRoLlBJIC8gMiksXG4gIGVhc2VJbk91dFNpbmU6ICh4KSA9PiAtKE1hdGguY29zKE1hdGguUEkgKiB4KSAtIDEpIC8gMixcbiAgZWFzZUluRXhwbzogKHgpID0+IHggPT09IDAgPyAwIDogTWF0aC5wb3coMiwgMTAgKiB4IC0gMTApLFxuICBlYXNlT3V0RXhwbzogKHgpID0+IHggPT09IDEgPyAxIDogMSAtIE1hdGgucG93KDIsIC0xMCAqIHgpLFxuICBlYXNlSW5PdXRFeHBvOiAoeCkgPT4geCA9PT0gMCA/IDAgOiB4ID09PSAxID8gMSA6IHggPCAwLjUgPyBNYXRoLnBvdygyLCAyMCAqIHggLSAxMCkgLyAyIDogKDIgLSBNYXRoLnBvdygyLCAtMjAgKiB4ICsgMTApKSAvIDIsXG4gIGVhc2VJbkNpcmM6ICh4KSA9PiAxIC0gTWF0aC5zcXJ0KDEgLSBNYXRoLnBvdyh4LCAyKSksXG4gIGVhc2VPdXRDaXJjOiAoeCkgPT4gTWF0aC5zcXJ0KDEgLSBNYXRoLnBvdyh4IC0gMSwgMikpLFxuICBlYXNlSW5PdXRDaXJjOiAoeCkgPT4geCA8IDAuNSA/ICgxIC0gTWF0aC5zcXJ0KDEgLSBNYXRoLnBvdygyICogeCwgMikpKSAvIDIgOiAoTWF0aC5zcXJ0KDEgLSBNYXRoLnBvdygtMiAqIHggKyAyLCAyKSkgKyAxKSAvIDIsXG4gIGVhc2VJbkJhY2s6ICh4KSA9PiBjMyAqIHggKiB4ICogeCAtIGMxICogeCAqIHgsXG4gIGVhc2VPdXRCYWNrOiAoeCkgPT4gMSArIGMzICogTWF0aC5wb3coeCAtIDEsIDMpICsgYzEgKiBNYXRoLnBvdyh4IC0gMSwgMiksXG4gIGVhc2VJbk91dEJhY2s6ICh4KSA9PiB4IDwgMC41ID8gTWF0aC5wb3coMiAqIHgsIDIpICogKChjMiArIDEpICogMiAqIHggLSBjMikgLyAyIDogKE1hdGgucG93KDIgKiB4IC0gMiwgMikgKiAoKGMyICsgMSkgKiAoeCAqIDIgLSAyKSArIGMyKSArIDIpIC8gMixcbiAgZWFzZUluRWxhc3RpYzogKHgpID0+IHggPT09IDAgPyAwIDogeCA9PT0gMSA/IDEgOiAtTWF0aC5wb3coMiwgMTAgKiB4IC0gMTApICogTWF0aC5zaW4oKHggKiAxMCAtIDEwLjc1KSAqIGM0KSxcbiAgZWFzZU91dEVsYXN0aWM6ICh4KSA9PiB4ID09PSAwID8gMCA6IHggPT09IDEgPyAxIDogTWF0aC5wb3coMiwgLTEwICogeCkgKiBNYXRoLnNpbigoeCAqIDEwIC0gMC43NSkgKiBjNCkgKyAxLFxuICBlYXNlSW5PdXRFbGFzdGljOiAoeCkgPT4geCA9PT0gMCA/IDAgOiB4ID09PSAxID8gMSA6IHggPCAwLjUgPyAtKE1hdGgucG93KDIsIDIwICogeCAtIDEwKSAqIE1hdGguc2luKCgyMCAqIHggLSAxMS4xMjUpICogYzUpKSAvIDIgOiBNYXRoLnBvdygyLCAtMjAgKiB4ICsgMTApICogTWF0aC5zaW4oKDIwICogeCAtIDExLjEyNSkgKiBjNSkgLyAyICsgMSxcbiAgZWFzZUluQm91bmNlOiAoeCkgPT4gMSAtIGJvdW5jZU91dCgxIC0geCksXG4gIGVhc2VPdXRCb3VuY2U6IGJvdW5jZU91dCxcbiAgZWFzZUluT3V0Qm91bmNlOiAoeCkgPT4geCA8IDAuNSA/ICgxIC0gYm91bmNlT3V0KDEgLSAyICogeCkpIC8gMiA6ICgxICsgYm91bmNlT3V0KDIgKiB4IC0gMSkpIC8gMixcbiAgc3RlcHNcbn07XG5cbi8vIHNyYy9mbHVpZHMudHNcbnZhciAkZ2V0ID0gU3ltYm9sLmZvcihcIkZsdWlkVmFsdWUuZ2V0XCIpO1xudmFyICRvYnNlcnZlcnMgPSBTeW1ib2wuZm9yKFwiRmx1aWRWYWx1ZS5vYnNlcnZlcnNcIik7XG52YXIgaGFzRmx1aWRWYWx1ZSA9IChhcmcpID0+IEJvb2xlYW4oYXJnICYmIGFyZ1skZ2V0XSk7XG52YXIgZ2V0Rmx1aWRWYWx1ZSA9IChhcmcpID0+IGFyZyAmJiBhcmdbJGdldF0gPyBhcmdbJGdldF0oKSA6IGFyZztcbnZhciBnZXRGbHVpZE9ic2VydmVycyA9ICh0YXJnZXQpID0+IHRhcmdldFskb2JzZXJ2ZXJzXSB8fCBudWxsO1xuZnVuY3Rpb24gY2FsbEZsdWlkT2JzZXJ2ZXIob2JzZXJ2ZXIyLCBldmVudCkge1xuICBpZiAob2JzZXJ2ZXIyLmV2ZW50T2JzZXJ2ZWQpIHtcbiAgICBvYnNlcnZlcjIuZXZlbnRPYnNlcnZlZChldmVudCk7XG4gIH0gZWxzZSB7XG4gICAgb2JzZXJ2ZXIyKGV2ZW50KTtcbiAgfVxufVxuZnVuY3Rpb24gY2FsbEZsdWlkT2JzZXJ2ZXJzKHRhcmdldCwgZXZlbnQpIHtcbiAgY29uc3Qgb2JzZXJ2ZXJzID0gdGFyZ2V0WyRvYnNlcnZlcnNdO1xuICBpZiAob2JzZXJ2ZXJzKSB7XG4gICAgb2JzZXJ2ZXJzLmZvckVhY2goKG9ic2VydmVyMikgPT4ge1xuICAgICAgY2FsbEZsdWlkT2JzZXJ2ZXIob2JzZXJ2ZXIyLCBldmVudCk7XG4gICAgfSk7XG4gIH1cbn1cbiRnZXQsICRvYnNlcnZlcnM7XG52YXIgRmx1aWRWYWx1ZSA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoZ2V0KSB7XG4gICAgaWYgKCFnZXQgJiYgIShnZXQgPSB0aGlzLmdldCkpIHtcbiAgICAgIHRocm93IEVycm9yKFwiVW5rbm93biBnZXR0ZXJcIik7XG4gICAgfVxuICAgIHNldEZsdWlkR2V0dGVyKHRoaXMsIGdldCk7XG4gIH1cbn07XG52YXIgc2V0Rmx1aWRHZXR0ZXIgPSAodGFyZ2V0LCBnZXQpID0+IHNldEhpZGRlbih0YXJnZXQsICRnZXQsIGdldCk7XG5mdW5jdGlvbiBhZGRGbHVpZE9ic2VydmVyKHRhcmdldCwgb2JzZXJ2ZXIyKSB7XG4gIGlmICh0YXJnZXRbJGdldF0pIHtcbiAgICBsZXQgb2JzZXJ2ZXJzID0gdGFyZ2V0WyRvYnNlcnZlcnNdO1xuICAgIGlmICghb2JzZXJ2ZXJzKSB7XG4gICAgICBzZXRIaWRkZW4odGFyZ2V0LCAkb2JzZXJ2ZXJzLCBvYnNlcnZlcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpKTtcbiAgICB9XG4gICAgaWYgKCFvYnNlcnZlcnMuaGFzKG9ic2VydmVyMikpIHtcbiAgICAgIG9ic2VydmVycy5hZGQob2JzZXJ2ZXIyKTtcbiAgICAgIGlmICh0YXJnZXQub2JzZXJ2ZXJBZGRlZCkge1xuICAgICAgICB0YXJnZXQub2JzZXJ2ZXJBZGRlZChvYnNlcnZlcnMuc2l6ZSwgb2JzZXJ2ZXIyKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG9ic2VydmVyMjtcbn1cbmZ1bmN0aW9uIHJlbW92ZUZsdWlkT2JzZXJ2ZXIodGFyZ2V0LCBvYnNlcnZlcjIpIHtcbiAgY29uc3Qgb2JzZXJ2ZXJzID0gdGFyZ2V0WyRvYnNlcnZlcnNdO1xuICBpZiAob2JzZXJ2ZXJzICYmIG9ic2VydmVycy5oYXMob2JzZXJ2ZXIyKSkge1xuICAgIGNvbnN0IGNvdW50ID0gb2JzZXJ2ZXJzLnNpemUgLSAxO1xuICAgIGlmIChjb3VudCkge1xuICAgICAgb2JzZXJ2ZXJzLmRlbGV0ZShvYnNlcnZlcjIpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0YXJnZXRbJG9ic2VydmVyc10gPSBudWxsO1xuICAgIH1cbiAgICBpZiAodGFyZ2V0Lm9ic2VydmVyUmVtb3ZlZCkge1xuICAgICAgdGFyZ2V0Lm9ic2VydmVyUmVtb3ZlZChjb3VudCwgb2JzZXJ2ZXIyKTtcbiAgICB9XG4gIH1cbn1cbnZhciBzZXRIaWRkZW4gPSAodGFyZ2V0LCBrZXksIHZhbHVlKSA9PiBPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBrZXksIHtcbiAgdmFsdWUsXG4gIHdyaXRhYmxlOiB0cnVlLFxuICBjb25maWd1cmFibGU6IHRydWVcbn0pO1xuXG4vLyBzcmMvcmVnZXhzLnRzXG52YXIgbnVtYmVyUmVnZXggPSAvWytcXC1dPyg/OjB8WzEtOV1cXGQqKSg/OlxcLlxcZCopPyg/OltlRV1bK1xcLV0/XFxkKyk/L2c7XG52YXIgY29sb3JSZWdleCA9IC8oIyg/OlswLTlhLWZdezJ9KXsyLDR9fCgjWzAtOWEtZl17M30pfChyZ2J8aHNsKWE/XFwoKC0/XFxkKyU/WyxcXHNdKyl7MiwzfVxccypbXFxkXFwuXSslP1xcKSkvZ2k7XG52YXIgdW5pdFJlZ2V4ID0gbmV3IFJlZ0V4cChgKCR7bnVtYmVyUmVnZXguc291cmNlfSkoJXxbYS16XSspYCwgXCJpXCIpO1xudmFyIHJnYmFSZWdleCA9IC9yZ2JhXFwoKFswLTlcXC4tXSspLCAoWzAtOVxcLi1dKyksIChbMC05XFwuLV0rKSwgKFswLTlcXC4tXSspXFwpL2dpO1xudmFyIGNzc1ZhcmlhYmxlUmVnZXggPSAvdmFyXFwoKC0tW2EtekEtWjAtOS1fXSspLD8gPyhbYS16QS1aMC05ICgpJSMuLC1dKyk/XFwpLztcblxuLy8gc3JjL3ZhcmlhYmxlVG9SZ2JhLnRzXG52YXIgdmFyaWFibGVUb1JnYmEgPSAoaW5wdXQpID0+IHtcbiAgY29uc3QgW3Rva2VuLCBmYWxsYmFja10gPSBwYXJzZUNTU1ZhcmlhYmxlKGlucHV0KTtcbiAgaWYgKCF0b2tlbiB8fCBpc1NTUigpKSB7XG4gICAgcmV0dXJuIGlucHV0O1xuICB9XG4gIGNvbnN0IHZhbHVlID0gd2luZG93LmdldENvbXB1dGVkU3R5bGUoZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50KS5nZXRQcm9wZXJ0eVZhbHVlKHRva2VuKTtcbiAgaWYgKHZhbHVlKSB7XG4gICAgcmV0dXJuIHZhbHVlLnRyaW0oKTtcbiAgfSBlbHNlIGlmIChmYWxsYmFjayAmJiBmYWxsYmFjay5zdGFydHNXaXRoKFwiLS1cIikpIHtcbiAgICBjb25zdCB2YWx1ZTIgPSB3aW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZShkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQpLmdldFByb3BlcnR5VmFsdWUoZmFsbGJhY2spO1xuICAgIGlmICh2YWx1ZTIpIHtcbiAgICAgIHJldHVybiB2YWx1ZTI7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBpbnB1dDtcbiAgICB9XG4gIH0gZWxzZSBpZiAoZmFsbGJhY2sgJiYgY3NzVmFyaWFibGVSZWdleC50ZXN0KGZhbGxiYWNrKSkge1xuICAgIHJldHVybiB2YXJpYWJsZVRvUmdiYShmYWxsYmFjayk7XG4gIH0gZWxzZSBpZiAoZmFsbGJhY2spIHtcbiAgICByZXR1cm4gZmFsbGJhY2s7XG4gIH1cbiAgcmV0dXJuIGlucHV0O1xufTtcbnZhciBwYXJzZUNTU1ZhcmlhYmxlID0gKGN1cnJlbnQpID0+IHtcbiAgY29uc3QgbWF0Y2ggPSBjc3NWYXJpYWJsZVJlZ2V4LmV4ZWMoY3VycmVudCk7XG4gIGlmICghbWF0Y2gpIHJldHVybiBbLF07XG4gIGNvbnN0IFssIHRva2VuLCBmYWxsYmFja10gPSBtYXRjaDtcbiAgcmV0dXJuIFt0b2tlbiwgZmFsbGJhY2tdO1xufTtcblxuLy8gc3JjL3N0cmluZ0ludGVycG9sYXRpb24udHNcbnZhciBuYW1lZENvbG9yUmVnZXg7XG52YXIgcmdiYVJvdW5kID0gKF8sIHAxLCBwMiwgcDMsIHA0KSA9PiBgcmdiYSgke01hdGgucm91bmQocDEpfSwgJHtNYXRoLnJvdW5kKHAyKX0sICR7TWF0aC5yb3VuZChwMyl9LCAke3A0fSlgO1xudmFyIGNyZWF0ZVN0cmluZ0ludGVycG9sYXRvcjIgPSAoY29uZmlnKSA9PiB7XG4gIGlmICghbmFtZWRDb2xvclJlZ2V4KVxuICAgIG5hbWVkQ29sb3JSZWdleCA9IGNvbG9ycyA/IChcbiAgICAgIC8vIG1hdGNoIGNvbG9yIG5hbWVzLCBpZ25vcmUgcGFydGlhbCBtYXRjaGVzXG4gICAgICBuZXcgUmVnRXhwKGAoJHtPYmplY3Qua2V5cyhjb2xvcnMpLmpvaW4oXCJ8XCIpfSkoPyFcXFxcdylgLCBcImdcIilcbiAgICApIDogKFxuICAgICAgLy8gbmV2ZXIgbWF0Y2hcbiAgICAgIC9eXFxiJC9cbiAgICApO1xuICBjb25zdCBvdXRwdXQgPSBjb25maWcub3V0cHV0Lm1hcCgodmFsdWUpID0+IHtcbiAgICByZXR1cm4gZ2V0Rmx1aWRWYWx1ZSh2YWx1ZSkucmVwbGFjZShjc3NWYXJpYWJsZVJlZ2V4LCB2YXJpYWJsZVRvUmdiYSkucmVwbGFjZShjb2xvclJlZ2V4LCBjb2xvclRvUmdiYSkucmVwbGFjZShuYW1lZENvbG9yUmVnZXgsIGNvbG9yVG9SZ2JhKTtcbiAgfSk7XG4gIGNvbnN0IGtleWZyYW1lcyA9IG91dHB1dC5tYXAoKHZhbHVlKSA9PiB2YWx1ZS5tYXRjaChudW1iZXJSZWdleCkubWFwKE51bWJlcikpO1xuICBjb25zdCBvdXRwdXRSYW5nZXMgPSBrZXlmcmFtZXNbMF0ubWFwKFxuICAgIChfLCBpKSA9PiBrZXlmcmFtZXMubWFwKCh2YWx1ZXMpID0+IHtcbiAgICAgIGlmICghKGkgaW4gdmFsdWVzKSkge1xuICAgICAgICB0aHJvdyBFcnJvcignVGhlIGFyaXR5IG9mIGVhY2ggXCJvdXRwdXRcIiB2YWx1ZSBtdXN0IGJlIGVxdWFsJyk7XG4gICAgICB9XG4gICAgICByZXR1cm4gdmFsdWVzW2ldO1xuICAgIH0pXG4gICk7XG4gIGNvbnN0IGludGVycG9sYXRvcnMgPSBvdXRwdXRSYW5nZXMubWFwKFxuICAgIChvdXRwdXQyKSA9PiBjcmVhdGVJbnRlcnBvbGF0b3IoeyAuLi5jb25maWcsIG91dHB1dDogb3V0cHV0MiB9KVxuICApO1xuICByZXR1cm4gKGlucHV0KSA9PiB7XG4gICAgY29uc3QgbWlzc2luZ1VuaXQgPSAhdW5pdFJlZ2V4LnRlc3Qob3V0cHV0WzBdKSAmJiBvdXRwdXQuZmluZCgodmFsdWUpID0+IHVuaXRSZWdleC50ZXN0KHZhbHVlKSk/LnJlcGxhY2UobnVtYmVyUmVnZXgsIFwiXCIpO1xuICAgIGxldCBpID0gMDtcbiAgICByZXR1cm4gb3V0cHV0WzBdLnJlcGxhY2UoXG4gICAgICBudW1iZXJSZWdleCxcbiAgICAgICgpID0+IGAke2ludGVycG9sYXRvcnNbaSsrXShpbnB1dCl9JHttaXNzaW5nVW5pdCB8fCBcIlwifWBcbiAgICApLnJlcGxhY2UocmdiYVJlZ2V4LCByZ2JhUm91bmQpO1xuICB9O1xufTtcblxuLy8gc3JjL2RlcHJlY2F0aW9ucy50c1xudmFyIHByZWZpeCA9IFwicmVhY3Qtc3ByaW5nOiBcIjtcbnZhciBvbmNlID0gKGZuKSA9PiB7XG4gIGNvbnN0IGZ1bmMgPSBmbjtcbiAgbGV0IGNhbGxlZCA9IGZhbHNlO1xuICBpZiAodHlwZW9mIGZ1bmMgIT0gXCJmdW5jdGlvblwiKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgJHtwcmVmaXh9b25jZSByZXF1aXJlcyBhIGZ1bmN0aW9uIHBhcmFtZXRlcmApO1xuICB9XG4gIHJldHVybiAoLi4uYXJncykgPT4ge1xuICAgIGlmICghY2FsbGVkKSB7XG4gICAgICBmdW5jKC4uLmFyZ3MpO1xuICAgICAgY2FsbGVkID0gdHJ1ZTtcbiAgICB9XG4gIH07XG59O1xudmFyIHdhcm5JbnRlcnBvbGF0ZSA9IG9uY2UoY29uc29sZS53YXJuKTtcbmZ1bmN0aW9uIGRlcHJlY2F0ZUludGVycG9sYXRlKCkge1xuICB3YXJuSW50ZXJwb2xhdGUoXG4gICAgYCR7cHJlZml4fVRoZSBcImludGVycG9sYXRlXCIgZnVuY3Rpb24gaXMgZGVwcmVjYXRlZCBpbiB2OSAodXNlIFwidG9cIiBpbnN0ZWFkKWBcbiAgKTtcbn1cbnZhciB3YXJuRGlyZWN0Q2FsbCA9IG9uY2UoY29uc29sZS53YXJuKTtcbmZ1bmN0aW9uIGRlcHJlY2F0ZURpcmVjdENhbGwoKSB7XG4gIHdhcm5EaXJlY3RDYWxsKFxuICAgIGAke3ByZWZpeH1EaXJlY3RseSBjYWxsaW5nIHN0YXJ0IGluc3RlYWQgb2YgdXNpbmcgdGhlIGFwaSBvYmplY3QgaXMgZGVwcmVjYXRlZCBpbiB2OSAodXNlIFwiLnN0YXJ0XCIgaW5zdGVhZCksIHRoaXMgd2lsbCBiZSByZW1vdmVkIGluIGxhdGVyIDAuWC4wIHZlcnNpb25zYFxuICApO1xufVxuXG4vLyBzcmMvaXNBbmltYXRlZFN0cmluZy50c1xuZnVuY3Rpb24gaXNBbmltYXRlZFN0cmluZyh2YWx1ZSkge1xuICByZXR1cm4gaXMuc3RyKHZhbHVlKSAmJiAodmFsdWVbMF0gPT0gXCIjXCIgfHwgL1xcZC8udGVzdCh2YWx1ZSkgfHwgLy8gRG8gbm90IGlkZW50aWZ5IGEgQ1NTIHZhcmlhYmxlIGFzIGFuIEFuaW1hdGVkU3RyaW5nIGlmIGl0cyBTU1JcbiAgIWlzU1NSKCkgJiYgY3NzVmFyaWFibGVSZWdleC50ZXN0KHZhbHVlKSB8fCB2YWx1ZSBpbiAoY29sb3JzIHx8IHt9KSk7XG59XG5cbi8vIHNyYy9kb20tZXZlbnRzL3Njcm9sbC9pbmRleC50c1xuaW1wb3J0IHsgcmFmIGFzIHJhZjMgfSBmcm9tIFwiQHJlYWN0LXNwcmluZy9yYWZ6XCI7XG5cbi8vIHNyYy9kb20tZXZlbnRzL3Jlc2l6ZS9yZXNpemVFbGVtZW50LnRzXG52YXIgb2JzZXJ2ZXI7XG52YXIgcmVzaXplSGFuZGxlcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFdlYWtNYXAoKTtcbnZhciBoYW5kbGVPYnNlcnZhdGlvbiA9IChlbnRyaWVzKSA9PiBlbnRyaWVzLmZvckVhY2goKHsgdGFyZ2V0LCBjb250ZW50UmVjdCB9KSA9PiB7XG4gIHJldHVybiByZXNpemVIYW5kbGVycy5nZXQodGFyZ2V0KT8uZm9yRWFjaCgoaGFuZGxlcikgPT4gaGFuZGxlcihjb250ZW50UmVjdCkpO1xufSk7XG5mdW5jdGlvbiByZXNpemVFbGVtZW50KGhhbmRsZXIsIHRhcmdldCkge1xuICBpZiAoIW9ic2VydmVyKSB7XG4gICAgaWYgKHR5cGVvZiBSZXNpemVPYnNlcnZlciAhPT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgb2JzZXJ2ZXIgPSBuZXcgUmVzaXplT2JzZXJ2ZXIoaGFuZGxlT2JzZXJ2YXRpb24pO1xuICAgIH1cbiAgfVxuICBsZXQgZWxlbWVudEhhbmRsZXJzID0gcmVzaXplSGFuZGxlcnMuZ2V0KHRhcmdldCk7XG4gIGlmICghZWxlbWVudEhhbmRsZXJzKSB7XG4gICAgZWxlbWVudEhhbmRsZXJzID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbiAgICByZXNpemVIYW5kbGVycy5zZXQodGFyZ2V0LCBlbGVtZW50SGFuZGxlcnMpO1xuICB9XG4gIGVsZW1lbnRIYW5kbGVycy5hZGQoaGFuZGxlcik7XG4gIGlmIChvYnNlcnZlcikge1xuICAgIG9ic2VydmVyLm9ic2VydmUodGFyZ2V0KTtcbiAgfVxuICByZXR1cm4gKCkgPT4ge1xuICAgIGNvbnN0IGVsZW1lbnRIYW5kbGVyczIgPSByZXNpemVIYW5kbGVycy5nZXQodGFyZ2V0KTtcbiAgICBpZiAoIWVsZW1lbnRIYW5kbGVyczIpIHJldHVybjtcbiAgICBlbGVtZW50SGFuZGxlcnMyLmRlbGV0ZShoYW5kbGVyKTtcbiAgICBpZiAoIWVsZW1lbnRIYW5kbGVyczIuc2l6ZSAmJiBvYnNlcnZlcikge1xuICAgICAgb2JzZXJ2ZXIudW5vYnNlcnZlKHRhcmdldCk7XG4gICAgfVxuICB9O1xufVxuXG4vLyBzcmMvZG9tLWV2ZW50cy9yZXNpemUvcmVzaXplV2luZG93LnRzXG52YXIgbGlzdGVuZXJzID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbnZhciBjbGVhbnVwV2luZG93UmVzaXplSGFuZGxlcjtcbnZhciBjcmVhdGVSZXNpemVIYW5kbGVyID0gKCkgPT4ge1xuICBjb25zdCBoYW5kbGVSZXNpemUgPSAoKSA9PiB7XG4gICAgbGlzdGVuZXJzLmZvckVhY2goXG4gICAgICAoY2FsbGJhY2spID0+IGNhbGxiYWNrKHtcbiAgICAgICAgd2lkdGg6IHdpbmRvdy5pbm5lcldpZHRoLFxuICAgICAgICBoZWlnaHQ6IHdpbmRvdy5pbm5lckhlaWdodFxuICAgICAgfSlcbiAgICApO1xuICB9O1xuICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCBoYW5kbGVSZXNpemUpO1xuICByZXR1cm4gKCkgPT4ge1xuICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIGhhbmRsZVJlc2l6ZSk7XG4gIH07XG59O1xudmFyIHJlc2l6ZVdpbmRvdyA9IChjYWxsYmFjaykgPT4ge1xuICBsaXN0ZW5lcnMuYWRkKGNhbGxiYWNrKTtcbiAgaWYgKCFjbGVhbnVwV2luZG93UmVzaXplSGFuZGxlcikge1xuICAgIGNsZWFudXBXaW5kb3dSZXNpemVIYW5kbGVyID0gY3JlYXRlUmVzaXplSGFuZGxlcigpO1xuICB9XG4gIHJldHVybiAoKSA9PiB7XG4gICAgbGlzdGVuZXJzLmRlbGV0ZShjYWxsYmFjayk7XG4gICAgaWYgKCFsaXN0ZW5lcnMuc2l6ZSAmJiBjbGVhbnVwV2luZG93UmVzaXplSGFuZGxlcikge1xuICAgICAgY2xlYW51cFdpbmRvd1Jlc2l6ZUhhbmRsZXIoKTtcbiAgICAgIGNsZWFudXBXaW5kb3dSZXNpemVIYW5kbGVyID0gdm9pZCAwO1xuICAgIH1cbiAgfTtcbn07XG5cbi8vIHNyYy9kb20tZXZlbnRzL3Jlc2l6ZS9pbmRleC50c1xudmFyIG9uUmVzaXplID0gKGNhbGxiYWNrLCB7IGNvbnRhaW5lciA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudCB9ID0ge30pID0+IHtcbiAgaWYgKGNvbnRhaW5lciA9PT0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50KSB7XG4gICAgcmV0dXJuIHJlc2l6ZVdpbmRvdyhjYWxsYmFjayk7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIHJlc2l6ZUVsZW1lbnQoY2FsbGJhY2ssIGNvbnRhaW5lcik7XG4gIH1cbn07XG5cbi8vIHNyYy9wcm9ncmVzcy50c1xudmFyIHByb2dyZXNzID0gKG1pbiwgbWF4LCB2YWx1ZSkgPT4gbWF4IC0gbWluID09PSAwID8gMSA6ICh2YWx1ZSAtIG1pbikgLyAobWF4IC0gbWluKTtcblxuLy8gc3JjL2RvbS1ldmVudHMvc2Nyb2xsL1Njcm9sbEhhbmRsZXIudHNcbnZhciBTQ1JPTExfS0VZUyA9IHtcbiAgeDoge1xuICAgIGxlbmd0aDogXCJXaWR0aFwiLFxuICAgIHBvc2l0aW9uOiBcIkxlZnRcIlxuICB9LFxuICB5OiB7XG4gICAgbGVuZ3RoOiBcIkhlaWdodFwiLFxuICAgIHBvc2l0aW9uOiBcIlRvcFwiXG4gIH1cbn07XG52YXIgU2Nyb2xsSGFuZGxlciA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoY2FsbGJhY2ssIGNvbnRhaW5lcikge1xuICAgIHRoaXMuY3JlYXRlQXhpcyA9ICgpID0+ICh7XG4gICAgICBjdXJyZW50OiAwLFxuICAgICAgcHJvZ3Jlc3M6IDAsXG4gICAgICBzY3JvbGxMZW5ndGg6IDBcbiAgICB9KTtcbiAgICB0aGlzLnVwZGF0ZUF4aXMgPSAoYXhpc05hbWUpID0+IHtcbiAgICAgIGNvbnN0IGF4aXMgPSB0aGlzLmluZm9bYXhpc05hbWVdO1xuICAgICAgY29uc3QgeyBsZW5ndGgsIHBvc2l0aW9uIH0gPSBTQ1JPTExfS0VZU1theGlzTmFtZV07XG4gICAgICBheGlzLmN1cnJlbnQgPSB0aGlzLmNvbnRhaW5lcltgc2Nyb2xsJHtwb3NpdGlvbn1gXTtcbiAgICAgIGF4aXMuc2Nyb2xsTGVuZ3RoID0gdGhpcy5jb250YWluZXJbYHNjcm9sbCR7bGVuZ3RofWBdIC0gdGhpcy5jb250YWluZXJbYGNsaWVudCR7bGVuZ3RofWBdO1xuICAgICAgYXhpcy5wcm9ncmVzcyA9IHByb2dyZXNzKDAsIGF4aXMuc2Nyb2xsTGVuZ3RoLCBheGlzLmN1cnJlbnQpO1xuICAgIH07XG4gICAgdGhpcy51cGRhdGUgPSAoKSA9PiB7XG4gICAgICB0aGlzLnVwZGF0ZUF4aXMoXCJ4XCIpO1xuICAgICAgdGhpcy51cGRhdGVBeGlzKFwieVwiKTtcbiAgICB9O1xuICAgIHRoaXMuc2VuZEV2ZW50ID0gKCkgPT4ge1xuICAgICAgdGhpcy5jYWxsYmFjayh0aGlzLmluZm8pO1xuICAgIH07XG4gICAgdGhpcy5hZHZhbmNlID0gKCkgPT4ge1xuICAgICAgdGhpcy51cGRhdGUoKTtcbiAgICAgIHRoaXMuc2VuZEV2ZW50KCk7XG4gICAgfTtcbiAgICB0aGlzLmNhbGxiYWNrID0gY2FsbGJhY2s7XG4gICAgdGhpcy5jb250YWluZXIgPSBjb250YWluZXI7XG4gICAgdGhpcy5pbmZvID0ge1xuICAgICAgdGltZTogMCxcbiAgICAgIHg6IHRoaXMuY3JlYXRlQXhpcygpLFxuICAgICAgeTogdGhpcy5jcmVhdGVBeGlzKClcbiAgICB9O1xuICB9XG59O1xuXG4vLyBzcmMvZG9tLWV2ZW50cy9zY3JvbGwvaW5kZXgudHNcbnZhciBzY3JvbGxMaXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFdlYWtNYXAoKTtcbnZhciByZXNpemVMaXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFdlYWtNYXAoKTtcbnZhciBvblNjcm9sbEhhbmRsZXJzID0gLyogQF9fUFVSRV9fICovIG5ldyBXZWFrTWFwKCk7XG52YXIgZ2V0VGFyZ2V0ID0gKGNvbnRhaW5lcikgPT4gY29udGFpbmVyID09PSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQgPyB3aW5kb3cgOiBjb250YWluZXI7XG52YXIgb25TY3JvbGwgPSAoY2FsbGJhY2ssIHsgY29udGFpbmVyID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50IH0gPSB7fSkgPT4ge1xuICBsZXQgY29udGFpbmVySGFuZGxlcnMgPSBvblNjcm9sbEhhbmRsZXJzLmdldChjb250YWluZXIpO1xuICBpZiAoIWNvbnRhaW5lckhhbmRsZXJzKSB7XG4gICAgY29udGFpbmVySGFuZGxlcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICAgIG9uU2Nyb2xsSGFuZGxlcnMuc2V0KGNvbnRhaW5lciwgY29udGFpbmVySGFuZGxlcnMpO1xuICB9XG4gIGNvbnN0IGNvbnRhaW5lckhhbmRsZXIgPSBuZXcgU2Nyb2xsSGFuZGxlcihjYWxsYmFjaywgY29udGFpbmVyKTtcbiAgY29udGFpbmVySGFuZGxlcnMuYWRkKGNvbnRhaW5lckhhbmRsZXIpO1xuICBpZiAoIXNjcm9sbExpc3RlbmVycy5oYXMoY29udGFpbmVyKSkge1xuICAgIGNvbnN0IGxpc3RlbmVyID0gKCkgPT4ge1xuICAgICAgY29udGFpbmVySGFuZGxlcnM/LmZvckVhY2goKGhhbmRsZXIpID0+IGhhbmRsZXIuYWR2YW5jZSgpKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH07XG4gICAgc2Nyb2xsTGlzdGVuZXJzLnNldChjb250YWluZXIsIGxpc3RlbmVyKTtcbiAgICBjb25zdCB0YXJnZXQgPSBnZXRUYXJnZXQoY29udGFpbmVyKTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCBsaXN0ZW5lciwgeyBwYXNzaXZlOiB0cnVlIH0pO1xuICAgIGlmIChjb250YWluZXIgIT09IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudCkge1xuICAgICAgcmVzaXplTGlzdGVuZXJzLnNldChjb250YWluZXIsIG9uUmVzaXplKGxpc3RlbmVyLCB7IGNvbnRhaW5lciB9KSk7XG4gICAgfVxuICAgIHRhcmdldC5hZGRFdmVudExpc3RlbmVyKFwic2Nyb2xsXCIsIGxpc3RlbmVyLCB7IHBhc3NpdmU6IHRydWUgfSk7XG4gIH1cbiAgY29uc3QgYW5pbWF0ZVNjcm9sbCA9IHNjcm9sbExpc3RlbmVycy5nZXQoY29udGFpbmVyKTtcbiAgcmFmMyhhbmltYXRlU2Nyb2xsKTtcbiAgcmV0dXJuICgpID0+IHtcbiAgICByYWYzLmNhbmNlbChhbmltYXRlU2Nyb2xsKTtcbiAgICBjb25zdCBjb250YWluZXJIYW5kbGVyczIgPSBvblNjcm9sbEhhbmRsZXJzLmdldChjb250YWluZXIpO1xuICAgIGlmICghY29udGFpbmVySGFuZGxlcnMyKSByZXR1cm47XG4gICAgY29udGFpbmVySGFuZGxlcnMyLmRlbGV0ZShjb250YWluZXJIYW5kbGVyKTtcbiAgICBpZiAoY29udGFpbmVySGFuZGxlcnMyLnNpemUpIHJldHVybjtcbiAgICBjb25zdCBsaXN0ZW5lciA9IHNjcm9sbExpc3RlbmVycy5nZXQoY29udGFpbmVyKTtcbiAgICBzY3JvbGxMaXN0ZW5lcnMuZGVsZXRlKGNvbnRhaW5lcik7XG4gICAgaWYgKGxpc3RlbmVyKSB7XG4gICAgICBnZXRUYXJnZXQoY29udGFpbmVyKS5yZW1vdmVFdmVudExpc3RlbmVyKFwic2Nyb2xsXCIsIGxpc3RlbmVyKTtcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIGxpc3RlbmVyKTtcbiAgICAgIHJlc2l6ZUxpc3RlbmVycy5nZXQoY29udGFpbmVyKT8uKCk7XG4gICAgfVxuICB9O1xufTtcblxuLy8gc3JjL2hvb2tzL3VzZUNvbnN0YW50LnRzXG5pbXBvcnQgeyB1c2VSZWYgfSBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZUNvbnN0YW50KGluaXQpIHtcbiAgY29uc3QgcmVmID0gdXNlUmVmKG51bGwpO1xuICBpZiAocmVmLmN1cnJlbnQgPT09IG51bGwpIHtcbiAgICByZWYuY3VycmVudCA9IGluaXQoKTtcbiAgfVxuICByZXR1cm4gcmVmLmN1cnJlbnQ7XG59XG5cbi8vIHNyYy9ob29rcy91c2VGb3JjZVVwZGF0ZS50c1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcblxuLy8gc3JjL2hvb2tzL3VzZUlzTW91bnRlZC50c1xuaW1wb3J0IHsgdXNlUmVmIGFzIHVzZVJlZjIgfSBmcm9tIFwicmVhY3RcIjtcblxuLy8gc3JjL2hvb2tzL3VzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QudHNcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IGlzU1NSKCkgPyB1c2VFZmZlY3QgOiB1c2VMYXlvdXRFZmZlY3Q7XG5cbi8vIHNyYy9ob29rcy91c2VJc01vdW50ZWQudHNcbnZhciB1c2VJc01vdW50ZWQgPSAoKSA9PiB7XG4gIGNvbnN0IGlzTW91bnRlZCA9IHVzZVJlZjIoZmFsc2UpO1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBpc01vdW50ZWQuY3VycmVudCA9IHRydWU7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlzTW91bnRlZC5jdXJyZW50ID0gZmFsc2U7XG4gICAgfTtcbiAgfSwgW10pO1xuICByZXR1cm4gaXNNb3VudGVkO1xufTtcblxuLy8gc3JjL2hvb2tzL3VzZUZvcmNlVXBkYXRlLnRzXG5mdW5jdGlvbiB1c2VGb3JjZVVwZGF0ZSgpIHtcbiAgY29uc3QgdXBkYXRlID0gdXNlU3RhdGUoKVsxXTtcbiAgY29uc3QgaXNNb3VudGVkID0gdXNlSXNNb3VudGVkKCk7XG4gIHJldHVybiAoKSA9PiB7XG4gICAgaWYgKGlzTW91bnRlZC5jdXJyZW50KSB7XG4gICAgICB1cGRhdGUoTWF0aC5yYW5kb20oKSk7XG4gICAgfVxuICB9O1xufVxuXG4vLyBzcmMvaG9va3MvdXNlTWVtb09uZS50c1xuaW1wb3J0IHsgdXNlRWZmZWN0IGFzIHVzZUVmZmVjdDIsIHVzZVJlZiBhcyB1c2VSZWYzLCB1c2VTdGF0ZSBhcyB1c2VTdGF0ZTIgfSBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZU1lbW9PbmUoZ2V0UmVzdWx0LCBpbnB1dHMpIHtcbiAgY29uc3QgW2luaXRpYWxdID0gdXNlU3RhdGUyKFxuICAgICgpID0+ICh7XG4gICAgICBpbnB1dHMsXG4gICAgICByZXN1bHQ6IGdldFJlc3VsdCgpXG4gICAgfSlcbiAgKTtcbiAgY29uc3QgY29tbWl0dGVkID0gdXNlUmVmMyh2b2lkIDApO1xuICBjb25zdCBwcmV2Q2FjaGUgPSBjb21taXR0ZWQuY3VycmVudDtcbiAgbGV0IGNhY2hlID0gcHJldkNhY2hlO1xuICBpZiAoY2FjaGUpIHtcbiAgICBjb25zdCB1c2VDYWNoZSA9IEJvb2xlYW4oXG4gICAgICBpbnB1dHMgJiYgY2FjaGUuaW5wdXRzICYmIGFyZUlucHV0c0VxdWFsKGlucHV0cywgY2FjaGUuaW5wdXRzKVxuICAgICk7XG4gICAgaWYgKCF1c2VDYWNoZSkge1xuICAgICAgY2FjaGUgPSB7XG4gICAgICAgIGlucHV0cyxcbiAgICAgICAgcmVzdWx0OiBnZXRSZXN1bHQoKVxuICAgICAgfTtcbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgY2FjaGUgPSBpbml0aWFsO1xuICB9XG4gIHVzZUVmZmVjdDIoKCkgPT4ge1xuICAgIGNvbW1pdHRlZC5jdXJyZW50ID0gY2FjaGU7XG4gICAgaWYgKHByZXZDYWNoZSA9PSBpbml0aWFsKSB7XG4gICAgICBpbml0aWFsLmlucHV0cyA9IGluaXRpYWwucmVzdWx0ID0gdm9pZCAwO1xuICAgIH1cbiAgfSwgW2NhY2hlXSk7XG4gIHJldHVybiBjYWNoZS5yZXN1bHQ7XG59XG5mdW5jdGlvbiBhcmVJbnB1dHNFcXVhbChuZXh0LCBwcmV2KSB7XG4gIGlmIChuZXh0Lmxlbmd0aCAhPT0gcHJldi5sZW5ndGgpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBuZXh0Lmxlbmd0aDsgaSsrKSB7XG4gICAgaWYgKG5leHRbaV0gIT09IHByZXZbaV0pIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHRydWU7XG59XG5cbi8vIHNyYy9ob29rcy91c2VPbmNlLnRzXG5pbXBvcnQgeyB1c2VFZmZlY3QgYXMgdXNlRWZmZWN0MyB9IGZyb20gXCJyZWFjdFwiO1xudmFyIHVzZU9uY2UgPSAoZWZmZWN0KSA9PiB1c2VFZmZlY3QzKGVmZmVjdCwgZW1wdHlEZXBzKTtcbnZhciBlbXB0eURlcHMgPSBbXTtcblxuLy8gc3JjL2hvb2tzL3VzZVByZXYudHNcbmltcG9ydCB7IHVzZUVmZmVjdCBhcyB1c2VFZmZlY3Q0LCB1c2VSZWYgYXMgdXNlUmVmNCB9IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlUHJldih2YWx1ZSkge1xuICBjb25zdCBwcmV2UmVmID0gdXNlUmVmNCh2b2lkIDApO1xuICB1c2VFZmZlY3Q0KCgpID0+IHtcbiAgICBwcmV2UmVmLmN1cnJlbnQgPSB2YWx1ZTtcbiAgfSk7XG4gIHJldHVybiBwcmV2UmVmLmN1cnJlbnQ7XG59XG5cbi8vIHNyYy9ob29rcy91c2VSZWR1Y2VkTW90aW9uLnRzXG5pbXBvcnQgeyB1c2VTdGF0ZSBhcyB1c2VTdGF0ZTMgfSBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VSZWR1Y2VkTW90aW9uID0gKCkgPT4ge1xuICBjb25zdCBbcmVkdWNlZE1vdGlvbiwgc2V0UmVkdWNlZE1vdGlvbl0gPSB1c2VTdGF0ZTMobnVsbCk7XG4gIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IG1xbCA9IHdpbmRvdy5tYXRjaE1lZGlhKFwiKHByZWZlcnMtcmVkdWNlZC1tb3Rpb24pXCIpO1xuICAgIGNvbnN0IGhhbmRsZU1lZGlhQ2hhbmdlID0gKGUpID0+IHtcbiAgICAgIHNldFJlZHVjZWRNb3Rpb24oZS5tYXRjaGVzKTtcbiAgICAgIGFzc2lnbih7XG4gICAgICAgIHNraXBBbmltYXRpb246IGUubWF0Y2hlc1xuICAgICAgfSk7XG4gICAgfTtcbiAgICBoYW5kbGVNZWRpYUNoYW5nZShtcWwpO1xuICAgIGlmIChtcWwuYWRkRXZlbnRMaXN0ZW5lcikge1xuICAgICAgbXFsLmFkZEV2ZW50TGlzdGVuZXIoXCJjaGFuZ2VcIiwgaGFuZGxlTWVkaWFDaGFuZ2UpO1xuICAgIH0gZWxzZSB7XG4gICAgICBtcWwuYWRkTGlzdGVuZXIoaGFuZGxlTWVkaWFDaGFuZ2UpO1xuICAgIH1cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKG1xbC5yZW1vdmVFdmVudExpc3RlbmVyKSB7XG4gICAgICAgIG1xbC5yZW1vdmVFdmVudExpc3RlbmVyKFwiY2hhbmdlXCIsIGhhbmRsZU1lZGlhQ2hhbmdlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG1xbC5yZW1vdmVMaXN0ZW5lcihoYW5kbGVNZWRpYUNoYW5nZSk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW10pO1xuICByZXR1cm4gcmVkdWNlZE1vdGlvbjtcbn07XG5cbi8vIHNyYy9pbmRleC50c1xuaW1wb3J0IHsgcmFmIGFzIHJhZjQgfSBmcm9tIFwiQHJlYWN0LXNwcmluZy9yYWZ6XCI7XG5leHBvcnQge1xuICBGbHVpZFZhbHVlLFxuICBnbG9iYWxzX2V4cG9ydHMgYXMgR2xvYmFscyxcbiAgYWRkRmx1aWRPYnNlcnZlcixcbiAgY2FsbEZsdWlkT2JzZXJ2ZXIsXG4gIGNhbGxGbHVpZE9ic2VydmVycyxcbiAgY2xhbXAsXG4gIGNvbG9yVG9SZ2JhLFxuICBjb2xvcnMyIGFzIGNvbG9ycyxcbiAgY3JlYXRlSW50ZXJwb2xhdG9yLFxuICBjcmVhdGVTdHJpbmdJbnRlcnBvbGF0b3IyIGFzIGNyZWF0ZVN0cmluZ0ludGVycG9sYXRvcixcbiAgZGVmaW5lSGlkZGVuLFxuICBkZXByZWNhdGVEaXJlY3RDYWxsLFxuICBkZXByZWNhdGVJbnRlcnBvbGF0ZSxcbiAgZWFjaCxcbiAgZWFjaFByb3AsXG4gIGVhc2luZ3MsXG4gIGZsdXNoLFxuICBmbHVzaENhbGxzLFxuICBmcmFtZUxvb3AsXG4gIGdldEZsdWlkT2JzZXJ2ZXJzLFxuICBnZXRGbHVpZFZhbHVlLFxuICBoYXNGbHVpZFZhbHVlLFxuICBoZXgzLFxuICBoZXg0LFxuICBoZXg2LFxuICBoZXg4LFxuICBoc2wsXG4gIGhzbGEsXG4gIGlzLFxuICBpc0FuaW1hdGVkU3RyaW5nLFxuICBpc0VxdWFsLFxuICBpc1NTUixcbiAgbm9vcCxcbiAgb25SZXNpemUsXG4gIG9uU2Nyb2xsLFxuICBvbmNlLFxuICBwcmVmaXgsXG4gIHJhZjQgYXMgcmFmLFxuICByZW1vdmVGbHVpZE9ic2VydmVyLFxuICByZ2IsXG4gIHJnYmEsXG4gIHNldEZsdWlkR2V0dGVyLFxuICB0b0FycmF5LFxuICB1c2VDb25zdGFudCxcbiAgdXNlRm9yY2VVcGRhdGUsXG4gIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QsXG4gIHVzZU1lbW9PbmUsXG4gIHVzZU9uY2UsXG4gIHVzZVByZXYsXG4gIHVzZVJlZHVjZWRNb3Rpb25cbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-spring/types/dist/react-spring_types.modern.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/@react-spring/types/dist/react-spring_types.modern.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Any: () => (/* binding */ Any)\n/* harmony export */ });\n// src/utils.ts\nvar Any = class {\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXNwcmluZy90eXBlcy9kaXN0L3JlYWN0LXNwcmluZ190eXBlcy5tb2Rlcm4ubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFHRSIsInNvdXJjZXMiOlsiRDpcXGlkbGVzY2FwZXJcXHZpa3JhbS1nYW1lXFxub2RlX21vZHVsZXNcXEByZWFjdC1zcHJpbmdcXHR5cGVzXFxkaXN0XFxyZWFjdC1zcHJpbmdfdHlwZXMubW9kZXJuLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMudHNcbnZhciBBbnkgPSBjbGFzcyB7XG59O1xuZXhwb3J0IHtcbiAgQW55XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/types/dist/react-spring_types.modern.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-spring/web/dist/react-spring_web.modern.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/@react-spring/web/dist/react-spring_web.modern.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Any: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Any),\n/* harmony export */   BailSignal: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.BailSignal),\n/* harmony export */   Controller: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Controller),\n/* harmony export */   FrameValue: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.FrameValue),\n/* harmony export */   Globals: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Globals),\n/* harmony export */   Interpolation: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Interpolation),\n/* harmony export */   Spring: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Spring),\n/* harmony export */   SpringContext: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.SpringContext),\n/* harmony export */   SpringRef: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.SpringRef),\n/* harmony export */   SpringValue: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.SpringValue),\n/* harmony export */   Trail: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Trail),\n/* harmony export */   Transition: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Transition),\n/* harmony export */   a: () => (/* binding */ animated),\n/* harmony export */   animated: () => (/* binding */ animated),\n/* harmony export */   config: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.config),\n/* harmony export */   createInterpolator: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.createInterpolator),\n/* harmony export */   easings: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.easings),\n/* harmony export */   inferTo: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.inferTo),\n/* harmony export */   interpolate: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.interpolate),\n/* harmony export */   to: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.to),\n/* harmony export */   update: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.update),\n/* harmony export */   useChain: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useChain),\n/* harmony export */   useInView: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useInView),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useIsomorphicLayoutEffect),\n/* harmony export */   useReducedMotion: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useReducedMotion),\n/* harmony export */   useResize: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useResize),\n/* harmony export */   useScroll: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useScroll),\n/* harmony export */   useSpring: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useSpring),\n/* harmony export */   useSpringRef: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useSpringRef),\n/* harmony export */   useSpringValue: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useSpringValue),\n/* harmony export */   useSprings: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useSprings),\n/* harmony export */   useTrail: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useTrail),\n/* harmony export */   useTransition: () => (/* reexport safe */ _react_spring_core__WEBPACK_IMPORTED_MODULE_0__.useTransition)\n/* harmony export */ });\n/* harmony import */ var _react_spring_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-spring/core */ \"(ssr)/./node_modules/@react-spring/core/dist/react-spring_core.modern.mjs\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-spring/shared */ \"(ssr)/./node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs\");\n/* harmony import */ var _react_spring_animated__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-spring/animated */ \"(ssr)/./node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs\");\n// src/index.ts\n\n\n\n\n\n// src/applyAnimatedValues.ts\nvar isCustomPropRE = /^--/;\nfunction dangerousStyleValue(name, value) {\n  if (value == null || typeof value === \"boolean\" || value === \"\") return \"\";\n  if (typeof value === \"number\" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))\n    return value + \"px\";\n  return (\"\" + value).trim();\n}\nvar attributeCache = {};\nfunction applyAnimatedValues(instance, props) {\n  if (!instance.nodeType || !instance.setAttribute) {\n    return false;\n  }\n  const isFilterElement = instance.nodeName === \"filter\" || instance.parentNode && instance.parentNode.nodeName === \"filter\";\n  const {\n    className,\n    style,\n    children,\n    scrollTop,\n    scrollLeft,\n    viewBox,\n    ...attributes\n  } = props;\n  const values = Object.values(attributes);\n  const names = Object.keys(attributes).map(\n    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(\n      /([A-Z])/g,\n      // Attributes are written in dash case\n      (n) => \"-\" + n.toLowerCase()\n    ))\n  );\n  if (children !== void 0) {\n    instance.textContent = children;\n  }\n  for (const name in style) {\n    if (style.hasOwnProperty(name)) {\n      const value = dangerousStyleValue(name, style[name]);\n      if (isCustomPropRE.test(name)) {\n        instance.style.setProperty(name, value);\n      } else {\n        instance.style[name] = value;\n      }\n    }\n  }\n  names.forEach((name, i) => {\n    instance.setAttribute(name, values[i]);\n  });\n  if (className !== void 0) {\n    instance.className = className;\n  }\n  if (scrollTop !== void 0) {\n    instance.scrollTop = scrollTop;\n  }\n  if (scrollLeft !== void 0) {\n    instance.scrollLeft = scrollLeft;\n  }\n  if (viewBox !== void 0) {\n    instance.setAttribute(\"viewBox\", viewBox);\n  }\n}\nvar isUnitlessNumber = {\n  animationIterationCount: true,\n  borderImageOutset: true,\n  borderImageSlice: true,\n  borderImageWidth: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  columns: true,\n  flex: true,\n  flexGrow: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  flexOrder: true,\n  gridRow: true,\n  gridRowEnd: true,\n  gridRowSpan: true,\n  gridRowStart: true,\n  gridColumn: true,\n  gridColumnEnd: true,\n  gridColumnSpan: true,\n  gridColumnStart: true,\n  fontWeight: true,\n  lineClamp: true,\n  lineHeight: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  // SVG-related properties\n  fillOpacity: true,\n  floodOpacity: true,\n  stopOpacity: true,\n  strokeDasharray: true,\n  strokeDashoffset: true,\n  strokeMiterlimit: true,\n  strokeOpacity: true,\n  strokeWidth: true\n};\nvar prefixKey = (prefix, key) => prefix + key.charAt(0).toUpperCase() + key.substring(1);\nvar prefixes = [\"Webkit\", \"Ms\", \"Moz\", \"O\"];\nisUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {\n  prefixes.forEach((prefix) => acc[prefixKey(prefix, prop)] = acc[prop]);\n  return acc;\n}, isUnitlessNumber);\n\n// src/AnimatedStyle.ts\n\n\nvar domTransforms = /^(matrix|translate|scale|rotate|skew)/;\nvar pxTransforms = /^(translate)/;\nvar degTransforms = /^(rotate|skew)/;\nvar addUnit = (value, unit) => _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.is.num(value) && value !== 0 ? value + unit : value;\nvar isValueIdentity = (value, id) => _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.is.arr(value) ? value.every((v) => isValueIdentity(v, id)) : _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.is.num(value) ? value === id : parseFloat(value) === id;\nvar AnimatedStyle = class extends _react_spring_animated__WEBPACK_IMPORTED_MODULE_3__.AnimatedObject {\n  constructor({ x, y, z, ...style }) {\n    const inputs = [];\n    const transforms = [];\n    if (x || y || z) {\n      inputs.push([x || 0, y || 0, z || 0]);\n      transforms.push((xyz) => [\n        `translate3d(${xyz.map((v) => addUnit(v, \"px\")).join(\",\")})`,\n        // prettier-ignore\n        isValueIdentity(xyz, 0)\n      ]);\n    }\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.eachProp)(style, (value, key) => {\n      if (key === \"transform\") {\n        inputs.push([value || \"\"]);\n        transforms.push((transform) => [transform, transform === \"\"]);\n      } else if (domTransforms.test(key)) {\n        delete style[key];\n        if (_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.is.und(value)) return;\n        const unit = pxTransforms.test(key) ? \"px\" : degTransforms.test(key) ? \"deg\" : \"\";\n        inputs.push((0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.toArray)(value));\n        transforms.push(\n          key === \"rotate3d\" ? ([x2, y2, z2, deg]) => [\n            `rotate3d(${x2},${y2},${z2},${addUnit(deg, unit)})`,\n            isValueIdentity(deg, 0)\n          ] : (input) => [\n            `${key}(${input.map((v) => addUnit(v, unit)).join(\",\")})`,\n            isValueIdentity(input, key.startsWith(\"scale\") ? 1 : 0)\n          ]\n        );\n      }\n    });\n    if (inputs.length) {\n      style.transform = new FluidTransform(inputs, transforms);\n    }\n    super(style);\n  }\n};\nvar FluidTransform = class extends _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.FluidValue {\n  constructor(inputs, transforms) {\n    super();\n    this.inputs = inputs;\n    this.transforms = transforms;\n    this._value = null;\n  }\n  get() {\n    return this._value || (this._value = this._get());\n  }\n  _get() {\n    let transform = \"\";\n    let identity = true;\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.each)(this.inputs, (input, i) => {\n      const arg1 = (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.getFluidValue)(input[0]);\n      const [t, id] = this.transforms[i](\n        _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.is.arr(arg1) ? arg1 : input.map(_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.getFluidValue)\n      );\n      transform += \" \" + t;\n      identity = identity && id;\n    });\n    return identity ? \"none\" : transform;\n  }\n  // Start observing our inputs once we have an observer.\n  observerAdded(count) {\n    if (count == 1)\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.each)(\n        this.inputs,\n        (input) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.each)(\n          input,\n          (value) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.hasFluidValue)(value) && (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.addFluidObserver)(value, this)\n        )\n      );\n  }\n  // Stop observing our inputs once we have no observers.\n  observerRemoved(count) {\n    if (count == 0)\n      (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.each)(\n        this.inputs,\n        (input) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.each)(\n          input,\n          (value) => (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.hasFluidValue)(value) && (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.removeFluidObserver)(value, this)\n        )\n      );\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._value = null;\n    }\n    (0,_react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.callFluidObservers)(this, event);\n  }\n};\n\n// src/primitives.ts\nvar primitives = [\n  \"a\",\n  \"abbr\",\n  \"address\",\n  \"area\",\n  \"article\",\n  \"aside\",\n  \"audio\",\n  \"b\",\n  \"base\",\n  \"bdi\",\n  \"bdo\",\n  \"big\",\n  \"blockquote\",\n  \"body\",\n  \"br\",\n  \"button\",\n  \"canvas\",\n  \"caption\",\n  \"cite\",\n  \"code\",\n  \"col\",\n  \"colgroup\",\n  \"data\",\n  \"datalist\",\n  \"dd\",\n  \"del\",\n  \"details\",\n  \"dfn\",\n  \"dialog\",\n  \"div\",\n  \"dl\",\n  \"dt\",\n  \"em\",\n  \"embed\",\n  \"fieldset\",\n  \"figcaption\",\n  \"figure\",\n  \"footer\",\n  \"form\",\n  \"h1\",\n  \"h2\",\n  \"h3\",\n  \"h4\",\n  \"h5\",\n  \"h6\",\n  \"head\",\n  \"header\",\n  \"hgroup\",\n  \"hr\",\n  \"html\",\n  \"i\",\n  \"iframe\",\n  \"img\",\n  \"input\",\n  \"ins\",\n  \"kbd\",\n  \"keygen\",\n  \"label\",\n  \"legend\",\n  \"li\",\n  \"link\",\n  \"main\",\n  \"map\",\n  \"mark\",\n  \"menu\",\n  \"menuitem\",\n  \"meta\",\n  \"meter\",\n  \"nav\",\n  \"noscript\",\n  \"object\",\n  \"ol\",\n  \"optgroup\",\n  \"option\",\n  \"output\",\n  \"p\",\n  \"param\",\n  \"picture\",\n  \"pre\",\n  \"progress\",\n  \"q\",\n  \"rp\",\n  \"rt\",\n  \"ruby\",\n  \"s\",\n  \"samp\",\n  \"script\",\n  \"section\",\n  \"select\",\n  \"small\",\n  \"source\",\n  \"span\",\n  \"strong\",\n  \"style\",\n  \"sub\",\n  \"summary\",\n  \"sup\",\n  \"table\",\n  \"tbody\",\n  \"td\",\n  \"textarea\",\n  \"tfoot\",\n  \"th\",\n  \"thead\",\n  \"time\",\n  \"title\",\n  \"tr\",\n  \"track\",\n  \"u\",\n  \"ul\",\n  \"var\",\n  \"video\",\n  \"wbr\",\n  // SVG\n  \"circle\",\n  \"clipPath\",\n  \"defs\",\n  \"ellipse\",\n  \"foreignObject\",\n  \"g\",\n  \"image\",\n  \"line\",\n  \"linearGradient\",\n  \"mask\",\n  \"path\",\n  \"pattern\",\n  \"polygon\",\n  \"polyline\",\n  \"radialGradient\",\n  \"rect\",\n  \"stop\",\n  \"svg\",\n  \"text\",\n  \"tspan\"\n];\n\n// src/index.ts\n\n_react_spring_core__WEBPACK_IMPORTED_MODULE_0__.Globals.assign({\n  batchedUpdates: react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates,\n  createStringInterpolator: _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.createStringInterpolator,\n  colors: _react_spring_shared__WEBPACK_IMPORTED_MODULE_2__.colors\n});\nvar host = (0,_react_spring_animated__WEBPACK_IMPORTED_MODULE_3__.createHost)(primitives, {\n  applyAnimatedValues,\n  createAnimatedStyle: (style) => new AnimatedStyle(style),\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props\n});\nvar animated = host.animated;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-spring/web/dist/react-spring_web.modern.mjs\n");

/***/ })

};
;