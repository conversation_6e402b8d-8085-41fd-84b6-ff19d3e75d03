import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatStatValue(value: number): string {
  return Math.max(0, Math.min(100, value)).toString();
}

export function getStatColor(value: number): string {
  if (value >= 80) return 'text-red-500';
  if (value >= 60) return 'text-orange-500';
  if (value >= 40) return 'text-yellow-500';
  if (value >= 20) return 'text-blue-500';
  return 'text-gray-500';
}

export function getRelationshipColor(relationship: string): string {
  switch (relationship) {
    case 'dominated': return 'text-purple-500';
    case 'corrupted': return 'text-red-500';
    case 'interested': return 'text-pink-500';
    case 'friend': return 'text-green-500';
    case 'acquaintance': return 'text-blue-500';
    default: return 'text-gray-500';
  }
}

export function canMakeChoice(requirements: Record<string, number>, playerStats: Record<string, number>): boolean {
  return Object.entries(requirements).every(([stat, required]) => 
    playerStats[stat] >= required
  );
}

export function getLocationName(location: string): string {
  const locationNames: Record<string, string> = {
    'dps_classroom': 'DPS Classroom',
    'dps_library': 'DPS Library',
    'dps_cafeteria': 'DPS Cafeteria',
    'dps_principal_office': 'Principal\'s Office',
    'dps_corridor': 'School Corridor',
    'home': 'Home',
    'market': 'Local Market',
    'park': 'Nehru Park',
  };
  return locationNames[location] || location;
}
