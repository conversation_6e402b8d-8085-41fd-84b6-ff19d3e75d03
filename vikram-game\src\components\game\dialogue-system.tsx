'use client';

import { useState, useEffect } from 'react';
import { useGameStore } from '@/lib/stores/game-store';
import { DialogueBox } from '@/components/ui/typewriter';
import { AnimatedCard } from '@/components/ui/animated-card';
import { canMakeChoice } from '@/lib/utils';
import { chapter1Scenes } from '@/lib/data/chapter1';
import type { DialogueNode, DialogueChoice } from '@/lib/types';
import { useSpring, animated } from '@react-spring/web';

interface DialogueSystemProps {
  sceneId: string;
  onSceneComplete?: () => void;
}

export function DialogueSystem({ sceneId, onSceneComplete }: DialogueSystemProps) {
  const { 
    player, 
    characters, 
    currentDialogue,
    makeChoice,
    startDialogue,
    endDialogue,
    updatePlayerStats,
    updateCharacterStats,
    setPlayerMood
  } = useGameStore();
  
  const [currentNodeIndex, setCurrentNodeIndex] = useState(0);
  const [showChoices, setShowChoices] = useState(false);
  const [dialogueComplete, setDialogueComplete] = useState(false);

  // Find the current scene
  const scene = chapter1Scenes.find(s => s.id === sceneId);
  const currentNode = scene?.dialogueTree[currentNodeIndex];

  const choicesSpring = useSpring({
    opacity: showChoices ? 1 : 0,
    transform: showChoices ? 'translateY(0px)' : 'translateY(20px)',
    config: { tension: 280, friction: 60 }
  });

  useEffect(() => {
    if (scene && !currentDialogue) {
      startDialogue(scene.dialogueTree[0].id, undefined, sceneId);
    }
  }, [scene, currentDialogue, startDialogue, sceneId]);

  const handleDialogueComplete = () => {
    if (currentNode?.choices && currentNode.choices.length > 0) {
      setShowChoices(true);
    } else if (currentNode?.autoAdvance && currentNode.nextNode) {
      // Auto advance to next node
      const nextNodeIndex = scene?.dialogueTree.findIndex(node => node.id === currentNode.nextNode);
      if (nextNodeIndex !== undefined && nextNodeIndex !== -1) {
        setCurrentNodeIndex(nextNodeIndex);
        setShowChoices(false);
      }
    } else {
      // End of dialogue
      setDialogueComplete(true);
      endDialogue();
      onSceneComplete?.();
    }
  };

  const handleChoiceSelect = (choice: DialogueChoice) => {
    // Apply choice effects
    makeChoice(choice, scene?.characters[0]);
    
    // Update mood based on choice type
    if (choice.playerStatEffects.manipulation) {
      setPlayerMood('Manipulative');
    } else if (choice.playerStatEffects.dominance) {
      setPlayerMood('Dominant');
    } else if (choice.playerStatEffects.charisma) {
      setPlayerMood('Charming');
    }

    // Find next node
    if (currentNode?.nextNode) {
      const nextNodeIndex = scene?.dialogueTree.findIndex(node => node.id === currentNode.nextNode);
      if (nextNodeIndex !== undefined && nextNodeIndex !== -1) {
        setCurrentNodeIndex(nextNodeIndex);
        setShowChoices(false);
      }
    } else {
      // End of dialogue
      setDialogueComplete(true);
      endDialogue();
      onSceneComplete?.();
    }
  };

  if (!scene || !currentNode || dialogueComplete) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center p-6">
      {/* Background overlay */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
      
      {/* Dialogue container */}
      <div className="relative w-full max-w-4xl space-y-4">
        {/* Main dialogue box */}
        <DialogueBox
          speaker={currentNode.speaker}
          text={currentNode.text}
          onComplete={handleDialogueComplete}
          className="w-full"
        />

        {/* Choices */}
        {showChoices && currentNode.choices && (
          <animated.div style={choicesSpring} className="space-y-3">
            {currentNode.choices.map((choice, index) => {
              const canSelect = canMakeChoice(choice.requirements, player.stats);
              const targetCharacter = scene.characters[0] ? characters[scene.characters[0]] : null;
              
              return (
                <AnimatedCard
                  key={choice.id}
                  delay={index * 100}
                  hover={canSelect}
                  onClick={canSelect ? () => handleChoiceSelect(choice) : undefined}
                  className={`
                    ${canSelect 
                      ? 'border-blue-500/50 hover:border-blue-400 cursor-pointer' 
                      : 'border-gray-600 opacity-50 cursor-not-allowed'
                    }
                  `}
                >
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <p className="text-white font-medium">{choice.hinglishText}</p>
                      <span className="text-xs text-gray-400 ml-4">#{index + 1}</span>
                    </div>
                    
                    <p className="text-sm text-gray-400 italic">{choice.text}</p>
                    
                    {/* Show requirements if not met */}
                    {!canSelect && Object.keys(choice.requirements).length > 0 && (
                      <div className="text-xs text-red-400">
                        Requires: {Object.entries(choice.requirements)
                          .map(([stat, value]) => `${stat} ${value}`)
                          .join(', ')}
                      </div>
                    )}
                    
                    {/* Show effects preview */}
                    {canSelect && (
                      <div className="flex space-x-4 text-xs">
                        {Object.entries(choice.playerStatEffects).length > 0 && (
                          <div className="text-green-400">
                            Player: {Object.entries(choice.playerStatEffects)
                              .map(([stat, value]) => `${stat} ${value > 0 ? '+' : ''}${value}`)
                              .join(', ')}
                          </div>
                        )}
                        {Object.entries(choice.targetStatEffects).length > 0 && targetCharacter && (
                          <div className="text-blue-400">
                            {targetCharacter.name}: {Object.entries(choice.targetStatEffects)
                              .map(([stat, value]) => `${stat} ${value > 0 ? '+' : ''}${value}`)
                              .join(', ')}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </AnimatedCard>
              );
            })}
          </animated.div>
        )}
      </div>
    </div>
  );
}
