// Game Types
export interface PlayerStats {
  charisma: number;
  intelligence: number;
  manipulation: number;
  wealth: number;
  dominance: number;
}

export interface TargetStats {
  lust: number;
  corruption: number;
  affection: number;
  fear: number;
  trust: number;
}

export interface Character {
  id: string;
  name: string;
  description: string;
  avatar?: string;
  stats: TargetStats;
  secrets: string[];
  dominated: boolean;
  profileRevealed: boolean;
  relationship: 'stranger' | 'acquaintance' | 'friend' | 'interested' | 'corrupted' | 'dominated';
}

export interface DialogueChoice {
  id: string;
  text: string;
  hinglishText: string;
  playerStatEffects: Partial<PlayerStats>;
  targetStatEffects: Partial<TargetStats>;
  requirements: Partial<PlayerStats>;
  unlocks?: string[];
  consequences?: string[];
}

export interface DialogueNode {
  id: string;
  speaker: string;
  text: string;
  choices: DialogueChoice[];
  conditions?: Partial<PlayerStats>;
  targetConditions?: Partial<TargetStats>;
  autoAdvance?: boolean;
  nextNode?: string;
}

export interface Scene {
  id: string;
  title: string;
  description: string;
  background?: string;
  characters: string[];
  dialogueTree: DialogueNode[];
  unlockConditions?: Partial<PlayerStats>;
  rewards?: {
    lustPoints?: number;
    dominationPoints?: number;
    items?: string[];
  };
}

export interface Quest {
  id: string;
  title: string;
  description: string;
  targetCharacter?: string;
  objectives: string[];
  completed: boolean;
  active: boolean;
  rewards?: {
    lustPoints?: number;
    dominationPoints?: number;
    items?: string[];
  };
}

export interface ShopItem {
  id: string;
  name: string;
  description: string;
  price: number;
  type: 'consumable' | 'permanent' | 'intel';
  effects: Partial<TargetStats>;
  requirements?: Partial<PlayerStats>;
  hinglishName: string;
}

export interface GameProgress {
  currentChapter: number;
  currentScene: string;
  unlockedScenes: string[];
  completedQuests: string[];
  discoveredSecrets: string[];
  achievements: string[];
}

export interface GameSettings {
  textSpeed: number;
  autoAdvance: boolean;
  skipSeen: boolean;
  volume: {
    master: number;
    music: number;
    sfx: number;
    voice: number;
  };
}

export type GameLocation = 
  | 'dps_classroom'
  | 'dps_library' 
  | 'dps_cafeteria'
  | 'dps_principal_office'
  | 'dps_corridor'
  | 'home'
  | 'market'
  | 'park';

export interface GameState {
  player: {
    name: string;
    stats: PlayerStats;
    lustPoints: number;
    dominationPoints: number;
    inventory: string[];
    location: GameLocation;
    mood: string;
    time: string;
  };
  characters: Record<string, Character>;
  progress: GameProgress;
  settings: GameSettings;
  currentDialogue?: {
    nodeId: string;
    characterId?: string;
    sceneId: string;
  };
  activeQuests: Quest[];
  shop: ShopItem[];
}
