import type { Scene, DialogueNode, Quest } from '../types';

export const chapter1Scenes: Scene[] = [
  {
    id: 'intro',
    title: 'The Serpent\'s Genesis',
    description: '<PERSON><PERSON><PERSON>\'s story begins in the halls of Delhi Public School',
    background: 'dps_classroom',
    characters: [],
    dialogueTree: [
      {
        id: 'intro_1',
        speaker: 'Narrator',
        text: '<PERSON><PERSON><PERSON> sits in the back corner of his Delhi Public School classroom, staring blankly at the blackboard. Three months since his father\'s death, and the world still feels hollow. But today... today something is different.',
        choices: [
          {
            id: 'intro_continue',
            text: 'Continue...',
            hinglishText: 'Aage badhte hain...',
            playerStatEffects: {},
            targetStatEffects: {},
            requirements: {},
          }
        ],
        autoAdvance: false,
        nextNode: 'system_awakening',
      },
      {
        id: 'system_awakening',
        speaker: 'System',
        text: 'Suddenly, a translucent blue interface materializes before <PERSON><PERSON><PERSON>\'s eyes. Text scrolls across his vision: "DELHI DOMINATION SYSTEM ACTIVATED. WELCOME, VIKRAM SINGH. YOUR JOURNEY TO ABSOLUTE POWER BEGINS NOW."',
        choices: [
          {
            id: 'system_wtf',
            text: 'What the hell is this?',
            hinglishText: 'Yeh kya bakchodi hai?',
            playerStatEffects: { intelligence: 1 },
            targetStatEffects: {},
            requirements: {},
          },
          {
            id: 'system_crazy',
            text: 'Am I going crazy?',
            hinglishText: 'Kya main pagal ho gaya hun?',
            playerStatEffects: { intelligence: 1 },
            targetStatEffects: {},
            requirements: {},
          }
        ],
        nextNode: 'target_identification',
      },
      {
        id: 'target_identification',
        speaker: 'Narrator',
        text: 'Vikram\'s gaze falls on Kavya Gupta, the class princess. She\'s laughing with her friends, completely oblivious to his existence. The System interface pulses: "TARGET IDENTIFIED. SCAN AVAILABLE."',
        choices: [
          {
            id: 'scan_kavya',
            text: 'Scan Kavya',
            hinglishText: 'Kavya ko scan karo',
            playerStatEffects: { intelligence: 1 },
            targetStatEffects: {},
            requirements: {},
            unlocks: ['kavya_profile'],
          },
          {
            id: 'ignore_system',
            text: 'Ignore the System',
            hinglishText: 'System ko ignore karo',
            playerStatEffects: {},
            targetStatEffects: {},
            requirements: {},
          }
        ],
        nextNode: 'first_approach',
      },
      {
        id: 'first_approach',
        speaker: 'Narrator',
        text: 'During the break, Vikram approaches Kavya\'s desk. She looks up, surprised that the \'invisible boy\' is talking to her.',
        choices: [],
        autoAdvance: true,
        nextNode: 'kavya_response',
      },
      {
        id: 'kavya_response',
        speaker: 'Kavya',
        text: 'Kya chahiye tumhe?',
        choices: [
          {
            id: 'compliment_intelligence',
            text: 'Compliment her intelligence',
            hinglishText: 'Tumhari intelligence kaafi impressive hai, Kavya',
            playerStatEffects: { charisma: 1 },
            targetStatEffects: { affection: 5, trust: 3 },
            requirements: {},
          },
          {
            id: 'mention_exam',
            text: 'Mention her exam performance',
            hinglishText: 'Last exam mein tumhara performance... interesting tha',
            playerStatEffects: { manipulation: 1 },
            targetStatEffects: { fear: 10, trust: -5 },
            requirements: { intelligence: 5 },
          },
          {
            id: 'stare_silently',
            text: 'Just stare silently',
            hinglishText: '*Chup chaap dekhte raho*',
            playerStatEffects: { dominance: 1 },
            targetStatEffects: { fear: 5, lust: 2 },
            requirements: {},
          }
        ],
        nextNode: 'chapter_end',
      },
      {
        id: 'chapter_end',
        speaker: 'System',
        text: 'FIRST CONTACT ESTABLISHED. ANALYZING TARGET RESPONSE... CHAPTER 1 COMPLETE. THE SERPENT HAS AWAKENED.',
        choices: [
          {
            id: 'continue_story',
            text: 'Continue to next scene...',
            hinglishText: 'Aage ki kahani...',
            playerStatEffects: {},
            targetStatEffects: {},
            requirements: {},
          }
        ],
        autoAdvance: false,
      }
    ],
    rewards: {
      lustPoints: 10,
      dominationPoints: 5,
    }
  },
  {
    id: 'second_encounter',
    title: 'Building Influence',
    description: 'Vikram\'s second interaction with Kavya',
    background: 'dps_classroom',
    characters: ['kavya'],
    unlockConditions: { intelligence: 6 },
    dialogueTree: [
      {
        id: 'second_start',
        speaker: 'Narrator',
        text: 'The next day, Kavya seems different when she sees Vikram approaching. Her reaction depends on yesterday\'s encounter...',
        choices: [],
        autoAdvance: true,
        nextNode: 'kavya_reaction',
        conditions: { manipulation: 1 },
      },
      {
        id: 'kavya_reaction',
        speaker: 'Kavya',
        text: 'Tum... tum phir aa gaye?',
        choices: [
          {
            id: 'reassure',
            text: 'Reassure her gently',
            hinglishText: 'Arre, dar kyun rahi ho? Main sirf baat karna chahta hun',
            playerStatEffects: { charisma: 1 },
            targetStatEffects: { fear: -3, trust: 5, affection: 3 },
            requirements: {},
          },
          {
            id: 'exploit_nervousness',
            text: 'Exploit her nervousness',
            hinglishText: 'Nervous lag rahi ho, Kavya. Koi baat chhupane ki zarurat nahi',
            playerStatEffects: { manipulation: 2, dominance: 1 },
            targetStatEffects: { fear: 8, corruption: 5 },
            requirements: { manipulation: 3 },
          },
          {
            id: 'ask_interests',
            text: 'Ask about her interests',
            hinglishText: 'Tumhe kya pasand hai karne mein? Studies ke alawa?',
            playerStatEffects: { charisma: 1 },
            targetStatEffects: { affection: 5, trust: 3 },
            requirements: {},
          }
        ],
        nextNode: 'building_rapport',
      },
      {
        id: 'building_rapport',
        speaker: 'Narrator',
        text: 'As the conversation continues, Vikram can feel the dynamic shifting. The System whispers guidance in his mind...',
        choices: [
          {
            id: 'suggest_study',
            text: 'Suggest studying together',
            hinglishText: 'Agar chahiye toh saath mein padh sakte hain kabhi',
            playerStatEffects: { intelligence: 1 },
            targetStatEffects: { affection: 3, trust: 5 },
            requirements: { intelligence: 4 },
          },
          {
            id: 'comment_appearance',
            text: 'Comment on her appearance',
            hinglishText: 'Aaj kuch alag lag rahi ho... accha lag raha hai',
            playerStatEffects: { charisma: 1 },
            targetStatEffects: { lust: 3, affection: 2 },
            requirements: { charisma: 3 },
          },
          {
            id: 'subtle_threat',
            text: 'Make a subtle threat',
            hinglishText: 'Tumhe pata hai, main bahut kuch jaanta hun... interesting cheezein',
            playerStatEffects: { manipulation: 2, dominance: 1 },
            targetStatEffects: { fear: 12, corruption: 3, trust: -8 },
            requirements: { manipulation: 4, dominance: 2 },
          }
        ],
        nextNode: 'scene_end',
      },
      {
        id: 'scene_end',
        speaker: 'System',
        text: 'TARGET RESPONSE ANALYZED. RELATIONSHIP PARAMETERS UPDATED. CONTINUE BUILDING INFLUENCE...',
        choices: [
          {
            id: 'end_scene',
            text: 'End scene',
            hinglishText: 'Scene khatam',
            playerStatEffects: {},
            targetStatEffects: {},
            requirements: {},
          }
        ],
        autoAdvance: false,
      }
    ],
    rewards: {
      lustPoints: 15,
      dominationPoints: 10,
    }
  }
];

export const chapter1Quests: Quest[] = [
  {
    id: 'analyze_kavya',
    title: 'Analyze Kavya',
    description: 'Learn about Kavya\'s weaknesses and desires',
    targetCharacter: 'kavya',
    objectives: [
      'Scan Kavya using the System',
      'Discover her insecurities',
      'Make first contact'
    ],
    completed: false,
    active: true,
    rewards: {
      lustPoints: 25,
      dominationPoints: 15,
    }
  },
  {
    id: 'build_influence',
    title: 'Build Influence',
    description: 'Continue interacting with targets to build your power',
    objectives: [
      'Have multiple conversations with Kavya',
      'Increase at least one of her stats by 10 points',
      'Unlock new dialogue options'
    ],
    completed: false,
    active: false,
    rewards: {
      lustPoints: 50,
      dominationPoints: 30,
    }
  }
];
