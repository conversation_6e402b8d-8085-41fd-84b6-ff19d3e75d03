#!/usr/bin/env python3
"""
Vikram: The Delhi Domination System
Main game entry point
"""

import pygame
import sys
import json
from typing import Dict, List, Any
from enum import Enum

# Initialize Pygame
pygame.init()

# Constants
SCREEN_WIDTH = 1280
SCREEN_HEIGHT = 720
FPS = 60

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
GOLD = (255, 215, 0)

class GameState(Enum):
    MENU = "menu"
    STORY = "story"
    DIALOGUE = "dialogue"
    SYSTEM_MENU = "system_menu"
    HAREM = "harem"
    SHOP = "shop"

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Vikram: The Delhi Domination System")
        self.clock = pygame.time.Clock()
        self.running = True
        self.state = GameState.MENU
        
        # Load fonts
        self.font_large = pygame.font.Font(None, 36)
        self.font_medium = pygame.font.Font(None, 24)
        self.font_small = pygame.font.Font(None, 18)
        
        # Game data
        self.player = None
        self.current_chapter = None
        self.dialogue_system = None
        
        # Initialize game systems
        self.init_game_systems()
    
    def init_game_systems(self):
        """Initialize all game systems"""
        from game_systems import Player, DialogueSystem, Chapter1
        
        self.player = Player()
        self.dialogue_system = DialogueSystem()
        self.current_chapter = Chapter1(self.player, self.dialogue_system)
    
    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.state == GameState.MENU:
                        self.running = False
                    else:
                        self.state = GameState.MENU
                elif event.key == pygame.K_SPACE:
                    if self.state == GameState.MENU:
                        self.state = GameState.STORY
                        self.current_chapter.start()
                elif event.key == pygame.K_s:
                    if self.state == GameState.STORY:
                        self.state = GameState.SYSTEM_MENU
                elif event.key == pygame.K_1:
                    if self.state == GameState.DIALOGUE:
                        self.dialogue_system.choose_option(0)
                elif event.key == pygame.K_2:
                    if self.state == GameState.DIALOGUE:
                        self.dialogue_system.choose_option(1)
                elif event.key == pygame.K_3:
                    if self.state == GameState.DIALOGUE:
                        self.dialogue_system.choose_option(2)
    
    def update(self):
        """Update game logic"""
        if self.state == GameState.STORY:
            self.current_chapter.update()
            if self.current_chapter.in_dialogue:
                self.state = GameState.DIALOGUE
        elif self.state == GameState.DIALOGUE:
            if not self.dialogue_system.active:
                self.state = GameState.STORY
    
    def render(self):
        """Render the game"""
        self.screen.fill(BLACK)
        
        if self.state == GameState.MENU:
            self.render_menu()
        elif self.state == GameState.STORY:
            self.render_story()
        elif self.state == GameState.DIALOGUE:
            self.render_dialogue()
        elif self.state == GameState.SYSTEM_MENU:
            self.render_system_menu()
        
        # Always render HUD if not in menu
        if self.state != GameState.MENU:
            self.render_hud()
        
        pygame.display.flip()
    
    def render_menu(self):
        """Render main menu"""
        title = self.font_large.render("VIKRAM: THE DELHI DOMINATION SYSTEM", True, GOLD)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(title, title_rect)
        
        subtitle = self.font_medium.render("Chapter 1: School Days - The Serpent's Genesis", True, WHITE)
        subtitle_rect = subtitle.get_rect(center=(SCREEN_WIDTH//2, 250))
        self.screen.blit(subtitle, subtitle_rect)
        
        start_text = self.font_medium.render("Press SPACE to Start", True, GREEN)
        start_rect = start_text.get_rect(center=(SCREEN_WIDTH//2, 400))
        self.screen.blit(start_text, start_rect)
        
        quit_text = self.font_small.render("Press ESC to Quit", True, GRAY)
        quit_rect = quit_text.get_rect(center=(SCREEN_WIDTH//2, 450))
        self.screen.blit(quit_text, quit_rect)
    
    def render_story(self):
        """Render story content"""
        if self.current_chapter:
            self.current_chapter.render(self.screen, self.font_medium, self.font_small)
    
    def render_dialogue(self):
        """Render dialogue system"""
        if self.dialogue_system:
            self.dialogue_system.render(self.screen, self.font_medium, self.font_small)
    
    def render_system_menu(self):
        """Render system menu"""
        # Background
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))
        
        # System menu
        menu_rect = pygame.Rect(200, 100, 880, 520)
        pygame.draw.rect(self.screen, DARK_GRAY, menu_rect)
        pygame.draw.rect(self.screen, WHITE, menu_rect, 2)
        
        # Title
        title = self.font_large.render("SYSTEM MENU", True, GOLD)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 150))
        self.screen.blit(title, title_rect)
        
        # Player stats
        y_offset = 200
        stats_title = self.font_medium.render("VIKRAM'S STATUS:", True, WHITE)
        self.screen.blit(stats_title, (220, y_offset))
        
        y_offset += 40
        for stat, value in self.player.stats.items():
            stat_text = self.font_small.render(f"{stat.upper()}: {value}", True, WHITE)
            self.screen.blit(stat_text, (240, y_offset))
            y_offset += 25
        
        # Instructions
        inst_text = self.font_small.render("Press ESC to return", True, GRAY)
        self.screen.blit(inst_text, (220, 580))
    
    def render_hud(self):
        """Render HUD at bottom of screen"""
        hud_rect = pygame.Rect(0, SCREEN_HEIGHT - 60, SCREEN_WIDTH, 60)
        pygame.draw.rect(self.screen, DARK_GRAY, hud_rect)
        pygame.draw.rect(self.screen, WHITE, hud_rect, 1)
        
        # HUD text
        hud_text = f"| VIKRAM SINGH | LOCATION: {self.player.location} | MOOD: {self.player.mood} | TIME: {self.player.time} |"
        if self.current_chapter and self.current_chapter.active_quest:
            hud_text += f" ACTIVE QUEST: {self.current_chapter.active_quest} |"
        
        text_surface = self.font_small.render(hud_text, True, WHITE)
        self.screen.blit(text_surface, (10, SCREEN_HEIGHT - 45))
        
        # Controls
        controls = "Press S for System Menu | ESC for Menu"
        controls_surface = self.font_small.render(controls, True, GRAY)
        self.screen.blit(controls_surface, (10, SCREEN_HEIGHT - 25))
    
    def run(self):
        """Main game loop"""
        while self.running:
            self.handle_events()
            self.update()
            self.render()
            self.clock.tick(FPS)
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = Game()
    game.run()
