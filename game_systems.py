"""
Core game systems for <PERSON><PERSON><PERSON>: The Delhi Domination System
"""

import pygame
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

@dataclass
class ShopItem:
    name: str
    description: str
    cost: int
    item_type: str  # "consumable", "permanent", "intel"
    effects: Dict[str, int]
    requirements: Dict[str, int]
    hinglish_name: str

class StatType(Enum):
    CHARISMA = "charisma"
    INTELLIGENCE = "intelligence"
    MANIPULATION = "manipulation"
    WEALTH = "wealth"
    DOMINANCE = "dominance"

class TargetStatType(Enum):
    LUST = "lust"
    CORRUPTION = "corruption"
    AFFECTION = "affection"
    FEAR = "fear"
    TRUST = "trust"

@dataclass
class DialogueChoice:
    text: str
    stat_effects: Dict[str, int]
    target_effects: Dict[str, int]
    requirements: Dict[str, int]
    hinglish_text: str

class Player:
    def __init__(self):
        self.name = "<PERSON><PERSON><PERSON>"
        self.stats = {
            StatType.CHARISMA.value: 5,
            StatType.INTELLIGENCE.value: 7,
            StatType.MANIPULATION.value: 3,
            StatType.WEALTH.value: 2,
            StatType.DOMINANCE.value: 1
        }
        self.lust_points = 0
        self.domination_points = 0
        self.location = "DPS Classroom"
        self.mood = "Calculating"
        self.time = "10:30 AM"
        self.harem = []
        self.inventory = []
        self.titles = []
    
    def add_stat(self, stat: str, amount: int):
        """Add to a stat"""
        if stat in self.stats:
            self.stats[stat] += amount
            if self.stats[stat] < 0:
                self.stats[stat] = 0
    
    def has_requirement(self, requirements: Dict[str, int]) -> bool:
        """Check if player meets requirements"""
        for stat, required_value in requirements.items():
            if self.stats.get(stat, 0) < required_value:
                return False
        return True

class Target:
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.stats = {
            TargetStatType.LUST.value: 0,
            TargetStatType.CORRUPTION.value: 0,
            TargetStatType.AFFECTION.value: 0,
            TargetStatType.FEAR.value: 0,
            TargetStatType.TRUST.value: 0
        }
        self.secrets = []
        self.dominated = False
        self.profile_revealed = False
    
    def add_stat(self, stat: str, amount: int):
        """Add to a target stat"""
        if stat in self.stats:
            self.stats[stat] += amount
            if self.stats[stat] < 0:
                self.stats[stat] = 0
            elif self.stats[stat] > 100:
                self.stats[stat] = 100
    
    def is_ready_for_domination(self) -> bool:
        """Check if target is ready for domination"""
        return (self.stats[TargetStatType.CORRUPTION.value] >= 60 and 
                (self.stats[TargetStatType.LUST.value] >= 50 or 
                 self.stats[TargetStatType.FEAR.value] >= 50))

class DialogueSystem:
    def __init__(self):
        self.active = False
        self.current_dialogue = None
        self.current_speaker = ""
        self.current_text = ""
        self.choices = []
        self.choice_callback = None
        self.target = None
    
    def start_dialogue(self, speaker: str, text: str, choices: List[DialogueChoice], 
                      target: Optional[Target] = None, callback=None):
        """Start a dialogue sequence"""
        self.active = True
        self.current_speaker = speaker
        self.current_text = text
        self.choices = choices
        self.choice_callback = callback
        self.target = target
    
    def choose_option(self, choice_index: int):
        """Choose a dialogue option"""
        if 0 <= choice_index < len(self.choices):
            choice = self.choices[choice_index]
            
            # Apply effects
            if self.choice_callback:
                self.choice_callback(choice, self.target)
            
            self.active = False
    
    def render(self, screen, font_medium, font_small):
        """Render dialogue interface"""
        if not self.active:
            return
        
        # Dialogue box
        dialogue_rect = pygame.Rect(50, 400, 1180, 270)
        pygame.draw.rect(screen, (32, 32, 32), dialogue_rect)
        pygame.draw.rect(screen, (255, 255, 255), dialogue_rect, 2)
        
        # Speaker name
        speaker_text = font_medium.render(self.current_speaker, True, (255, 215, 0))
        screen.blit(speaker_text, (70, 420))
        
        # Dialogue text (word wrap)
        words = self.current_text.split(' ')
        lines = []
        current_line = ""
        max_width = 1100
        
        for word in words:
            test_line = current_line + word + " "
            if font_small.size(test_line)[0] < max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line.strip())
                current_line = word + " "
        if current_line:
            lines.append(current_line.strip())
        
        y_offset = 450
        for line in lines[:4]:  # Max 4 lines
            text_surface = font_small.render(line, True, (255, 255, 255))
            screen.blit(text_surface, (70, y_offset))
            y_offset += 25
        
        # Choices
        y_offset = 580
        for i, choice in enumerate(self.choices):
            choice_text = f"{i+1}. {choice.hinglish_text}"
            color = (0, 255, 0) if len(choice.requirements) == 0 else (255, 255, 0)
            
            # Check if choice is available
            # This would need player reference - simplified for now
            
            choice_surface = font_small.render(choice_text, True, color)
            screen.blit(choice_surface, (70, y_offset))
            y_offset += 25

class Quest:
    def __init__(self, name: str, description: str, target_name: str = ""):
        self.name = name
        self.description = description
        self.target_name = target_name
        self.completed = False
        self.active = True
    
    def complete(self):
        self.completed = True
        self.active = False

class Chapter1:
    def __init__(self, player: Player, dialogue_system: DialogueSystem):
        self.player = player
        self.dialogue_system = dialogue_system
        self.scene_index = 0
        self.in_dialogue = False
        self.active_quest = None
        
        # Create Kavya as first target
        self.kavya = Target("Kavya Gupta", "Popular girl in class, thinks she's untouchable")
        self.kavya.secrets = ["Has a crush on the cricket captain", "Cheated on last exam"]
        
        # Story scenes
        self.scenes = [
            self.intro_scene,
            self.system_awakening,
            self.first_target_encounter,
            self.kavya_analysis,
            self.first_interaction,
            self.second_kavya_encounter,
            self.kavya_private_moment,
            self.chapter_conclusion
        ]
    
    def start(self):
        """Start the chapter"""
        self.scene_index = 0
        self.player.location = "DPS Classroom"
        self.player.mood = "Grieving"
        self.player.time = "9:00 AM"
    
    def update(self):
        """Update chapter logic"""
        if self.scene_index < len(self.scenes) and not self.in_dialogue:
            self.scenes[self.scene_index]()
    
    def next_scene(self):
        """Move to next scene"""
        self.scene_index += 1
        self.in_dialogue = False
    
    def intro_scene(self):
        """Opening scene - Vikram's introduction"""
        self.in_dialogue = True
        
        intro_text = ("Vikram Singh sits in the back corner of his Delhi Public School classroom, "
                     "staring blankly at the blackboard. Three months since his father's death, "
                     "and the world still feels hollow. But today... today something is different.")
        
        choices = [
            DialogueChoice(
                "Continue...",
                {},
                {},
                {},
                "Aage badhte hain..."
            )
        ]
        
        self.dialogue_system.start_dialogue(
            "Narrator", 
            intro_text, 
            choices, 
            callback=lambda choice, target: self.next_scene()
        )
    
    def system_awakening(self):
        """The System awakens"""
        self.in_dialogue = True
        
        system_text = ("Suddenly, a translucent blue interface materializes before Vikram's eyes. "
                      "Text scrolls across his vision: 'DELHI DOMINATION SYSTEM ACTIVATED. "
                      "WELCOME, VIKRAM SINGH. YOUR JOURNEY TO ABSOLUTE POWER BEGINS NOW.'")
        
        choices = [
            DialogueChoice(
                "What the hell is this?",
                {},
                {},
                {},
                "Yeh kya bakchodi hai?"
            ),
            DialogueChoice(
                "Am I going crazy?",
                {},
                {},
                {},
                "Kya main pagal ho gaya hun?"
            )
        ]
        
        self.dialogue_system.start_dialogue(
            "System Awakening", 
            system_text, 
            choices, 
            callback=self.handle_system_awakening
        )
    
    def handle_system_awakening(self, choice: DialogueChoice, target):
        """Handle system awakening choice"""
        self.player.add_stat("intelligence", 1)
        self.next_scene()
    
    def first_target_encounter(self):
        """Vikram notices Kavya"""
        self.in_dialogue = True
        
        encounter_text = ("Vikram's gaze falls on Kavya Gupta, the class princess. "
                         "She's laughing with her friends, completely oblivious to his existence. "
                         "The System interface pulses: 'TARGET IDENTIFIED. SCAN AVAILABLE.'")
        
        choices = [
            DialogueChoice(
                "Scan Kavya",
                {"intelligence": 1},
                {},
                {},
                "Kavya ko scan karo"
            ),
            DialogueChoice(
                "Ignore the System",
                {},
                {},
                {},
                "System ko ignore karo"
            )
        ]
        
        self.dialogue_system.start_dialogue(
            "Target Identification", 
            encounter_text, 
            choices, 
            self.kavya,
            callback=self.handle_target_encounter
        )
    
    def handle_target_encounter(self, choice: DialogueChoice, target):
        """Handle target encounter choice"""
        if "Scan" in choice.text:
            self.kavya.profile_revealed = True
            self.active_quest = Quest("Analyze Kavya", "Learn about Kavya's weaknesses", "Kavya Gupta")
        
        # Apply choice effects
        for stat, value in choice.stat_effects.items():
            self.player.add_stat(stat, value)
        
        self.next_scene()
    
    def kavya_analysis(self):
        """Show Kavya's profile if scanned"""
        if not self.kavya.profile_revealed:
            self.next_scene()
            return
        
        self.in_dialogue = True
        
        analysis_text = ("TARGET PROFILE - KAVYA GUPTA:\n"
                        f"Lust: {self.kavya.stats['lust']}/100\n"
                        f"Corruption: {self.kavya.stats['corruption']}/100\n"
                        f"Affection: {self.kavya.stats['affection']}/100\n"
                        f"Fear: {self.kavya.stats['fear']}/100\n"
                        f"Trust: {self.kavya.stats['trust']}/100\n\n"
                        "WEAKNESS DETECTED: Insecure about her grades despite her popularity.")
        
        choices = [
            DialogueChoice(
                "Interesting...",
                {"manipulation": 1},
                {},
                {},
                "Dilchasp..."
            )
        ]
        
        self.dialogue_system.start_dialogue(
            "System Analysis", 
            analysis_text, 
            choices, 
            callback=lambda choice, target: self.next_scene()
        )
    
    def first_interaction(self):
        """First interaction with Kavya"""
        self.in_dialogue = True

        interaction_text = ("During the break, Vikram approaches Kavya's desk. "
                           "She looks up, surprised that the 'invisible boy' is talking to her. "
                           "'Kya chahiye tumhe?' she asks with a mix of curiosity and disdain.")

        choices = [
            DialogueChoice(
                "Compliment her intelligence",
                {"charisma": 1},
                {"affection": 5, "trust": 3},
                {},
                "Tumhari intelligence kaafi impressive hai, Kavya"
            ),
            DialogueChoice(
                "Mention her exam performance",
                {"manipulation": 1},
                {"fear": 10, "trust": -5},
                {"intelligence": 5},
                "Last exam mein tumhara performance... interesting tha"
            ),
            DialogueChoice(
                "Just stare silently",
                {"dominance": 1},
                {"fear": 5, "lust": 2},
                {},
                "*Chup chaap dekhte raho*"
            )
        ]

        self.dialogue_system.start_dialogue(
            "Kavya",
            interaction_text,
            choices,
            self.kavya,
            callback=self.handle_first_interaction
        )

    def second_kavya_encounter(self):
        """Second encounter with Kavya - building on first interaction"""
        self.in_dialogue = True

        # Different text based on previous choices
        if self.kavya.stats["fear"] > 5:
            encounter_text = ("The next day, Kavya seems nervous when she sees Vikram approaching. "
                             "She fidgets with her pen and avoids eye contact. "
                             "'Tum... tum phir aa gaye?' she stammers, clearly unsettled.")
        elif self.kavya.stats["affection"] > 3:
            encounter_text = ("Kavya looks up with a slight smile as Vikram approaches. "
                             "She seems more comfortable now, even curious about what he wants. "
                             "'Hi Vikram, kya baat hai?' she asks with genuine interest.")
        else:
            encounter_text = ("Kavya notices Vikram coming towards her desk again. "
                             "She raises an eyebrow, wondering what this quiet boy wants now. "
                             "'Dobara? Kuch kaam hai kya?' she asks, slightly annoyed.")

        choices = []

        # Different choices based on stats
        if self.kavya.stats["fear"] > 5:
            choices.extend([
                DialogueChoice(
                    "Reassure her gently",
                    {"charisma": 1},
                    {"fear": -3, "trust": 5, "affection": 3},
                    {},
                    "Arre, dar kyun rahi ho? Main sirf baat karna chahta hun"
                ),
                DialogueChoice(
                    "Exploit her nervousness",
                    {"manipulation": 2, "dominance": 1},
                    {"fear": 8, "corruption": 5},
                    {"manipulation": 3},
                    "Nervous lag rahi ho, Kavya. Koi baat chhupane ki zarurat nahi"
                )
            ])

        if self.kavya.stats["affection"] > 3:
            choices.extend([
                DialogueChoice(
                    "Ask about her interests",
                    {"charisma": 1},
                    {"affection": 5, "trust": 3},
                    {},
                    "Tumhe kya pasand hai karne mein? Studies ke alawa?"
                ),
                DialogueChoice(
                    "Suggest studying together",
                    {"intelligence": 1},
                    {"affection": 3, "trust": 5},
                    {"intelligence": 4},
                    "Agar chahiye toh saath mein padh sakte hain kabhi"
                )
            ])

        # Always available choices
        choices.extend([
            DialogueChoice(
                "Comment on her appearance",
                {"charisma": 1},
                {"lust": 3, "affection": 2},
                {"charisma": 3},
                "Aaj kuch alag lag rahi ho... accha lag raha hai"
            ),
            DialogueChoice(
                "Make a subtle threat",
                {"manipulation": 2, "dominance": 1},
                {"fear": 12, "corruption": 3, "trust": -8},
                {"manipulation": 4, "dominance": 2},
                "Tumhe pata hai, main bahut kuch jaanta hun... interesting cheezein"
            )
        ])

        self.dialogue_system.start_dialogue(
            "Kavya",
            encounter_text,
            choices,
            self.kavya,
            callback=self.handle_second_encounter
        )

    def kavya_private_moment(self):
        """A more intimate/private encounter with Kavya"""
        self.in_dialogue = True

        if self.kavya.stats["corruption"] > 10 and self.kavya.stats["fear"] > 15:
            private_text = ("After school, Vikram finds Kavya alone in the empty classroom. "
                           "She's packing her bag with shaky hands, clearly aware of his presence. "
                           "'Vikram... please, main kuch galat nahi kiya hai,' she whispers, "
                           "her voice trembling with a mix of fear and something else...")

            choices = [
                DialogueChoice(
                    "Move closer intimidatingly",
                    {"dominance": 2, "manipulation": 1},
                    {"fear": 15, "corruption": 8, "lust": 5},
                    {"dominance": 3},
                    "*Dheere se uske paas jaao* Galat? Kaun keh raha hai galat kiya hai?"
                ),
                DialogueChoice(
                    "Speak softly but menacingly",
                    {"manipulation": 2, "intelligence": 1},
                    {"fear": 10, "corruption": 12, "trust": -5},
                    {"manipulation": 5},
                    "Kavya... tumhe pata hai main tumhare baare mein kya jaanta hun"
                ),
                DialogueChoice(
                    "Offer false comfort",
                    {"charisma": 1, "manipulation": 2},
                    {"trust": 3, "corruption": 5, "affection": -2},
                    {"charisma": 4},
                    "Shh... main tumhara dost hun. Trust karo mujhpe"
                )
            ]

        elif self.kavya.stats["affection"] > 10 and self.kavya.stats["lust"] > 5:
            private_text = ("Kavya approaches Vikram after class, her cheeks slightly flushed. "
                           "'Vikram, main tumse kuch kehna chahti hun... privately,' she says, "
                           "glancing around to make sure no one is listening. There's something "
                           "different in her eyes - a curiosity, maybe even desire.")

            choices = [
                DialogueChoice(
                    "Show interest",
                    {"charisma": 2},
                    {"affection": 8, "lust": 5, "trust": 3},
                    {},
                    "Haan Kavya, bolo. Main sun raha hun"
                ),
                DialogueChoice(
                    "Be playfully dominant",
                    {"dominance": 1, "charisma": 1},
                    {"lust": 8, "corruption": 3, "affection": 2},
                    {"dominance": 2, "charisma": 3},
                    "Private? Interesting... kya baat hai jo sirf mujhse kehna hai?"
                ),
                DialogueChoice(
                    "Tease her",
                    {"manipulation": 1, "charisma": 1},
                    {"lust": 6, "affection": -1, "corruption": 2},
                    {"charisma": 4},
                    "Oho, Kavya Gupta ko mujhse private baat karni hai? Kya zamana aa gaya"
                )
            ]

        else:
            # Default private encounter
            private_text = ("Vikram catches Kavya alone near the school library. "
                           "She's reading a book, completely absorbed. When she notices him, "
                           "she looks up with a mixture of surprise and wariness. "
                           "'Yahan kya kar rahe ho?' she asks quietly.")

            choices = [
                DialogueChoice(
                    "Sit beside her",
                    {"charisma": 1},
                    {"affection": 3, "lust": 2, "trust": 1},
                    {},
                    "*Uske paas baith jaao* Bas tumhe dekh raha tha"
                ),
                DialogueChoice(
                    "Ask about the book",
                    {"intelligence": 1},
                    {"affection": 4, "trust": 3},
                    {},
                    "Kya padh rahi ho? Interesting lag raha hai"
                ),
                DialogueChoice(
                    "Make her uncomfortable",
                    {"manipulation": 1, "dominance": 1},
                    {"fear": 8, "corruption": 3, "trust": -3},
                    {"manipulation": 3},
                    "Akeli ho... perfect. Hum dono ko baat karni chahiye"
                )
            ]

        self.dialogue_system.start_dialogue(
            "Kavya - Private",
            private_text,
            choices,
            self.kavya,
            callback=self.handle_private_encounter
        )
    
    def handle_first_interaction(self, choice: DialogueChoice, target):
        """Handle first interaction with Kavya"""
        # Apply effects to player
        for stat, value in choice.stat_effects.items():
            self.player.add_stat(stat, value)

        # Apply effects to target
        if target:
            for stat, value in choice.target_effects.items():
                target.add_stat(stat, value)

        # Update quest
        if self.active_quest:
            self.active_quest.complete()
            self.active_quest = Quest("Build Influence", "Continue interacting with targets", "")

        self.player.mood = "Calculating"
        self.next_scene()

    def handle_second_encounter(self, choice: DialogueChoice, target):
        """Handle second encounter with Kavya"""
        # Apply effects to player
        for stat, value in choice.stat_effects.items():
            self.player.add_stat(stat, value)

        # Apply effects to target
        if target:
            for stat, value in choice.target_effects.items():
                target.add_stat(stat, value)

        # Check if Kavya is ready for more advanced interactions
        if target and target.stats["corruption"] > 15:
            self.active_quest = Quest("Corrupt Kavya", "Push Kavya further down the path of corruption", "Kavya")
            self.player.mood = "Predatory"
        elif target and target.stats["affection"] > 15:
            self.active_quest = Quest("Seduce Kavya", "Win Kavya's heart and body", "Kavya")
            self.player.mood = "Charming"
        else:
            self.active_quest = Quest("Understand Kavya", "Learn more about Kavya's desires and fears", "Kavya")
            self.player.mood = "Observant"

        self.next_scene()

    def handle_private_encounter(self, choice: DialogueChoice, target):
        """Handle private encounter with Kavya"""
        # Apply effects to player
        for stat, value in choice.stat_effects.items():
            self.player.add_stat(stat, value)

        # Apply effects to target
        if target:
            for stat, value in choice.target_effects.items():
                target.add_stat(stat, value)

        # Check for domination readiness
        if target and target.is_ready_for_domination():
            self.active_quest = Quest("Dominate Kavya", "Kavya is ready for complete domination", "Kavya")
            self.player.mood = "Dominant"
            # Award points for reaching this milestone
            self.player.lust_points += 50
            self.player.domination_points += 25
        elif target and (target.stats["corruption"] > 20 or target.stats["lust"] > 20):
            self.active_quest = Quest("Advanced Manipulation", "Kavya is becoming more susceptible", "Kavya")
            self.player.mood = "Manipulative"
            self.player.lust_points += 20
        else:
            self.player.mood = "Patient"

        self.next_scene()

    def chapter_conclusion(self):
        """Chapter 1 conclusion with stats summary"""
        self.in_dialogue = True

        # Generate conclusion based on Kavya's stats
        if self.kavya.is_ready_for_domination():
            conclusion_text = ("As the school day ends, Vikram reflects on his progress. "
                              "Kavya Gupta, once the untouchable princess of the class, "
                              "now trembles at his very presence. The System pulses with satisfaction: "
                              "'FIRST TARGET READY FOR DOMINATION. WELL DONE, VIKRAM.' "
                              "The serpent has awakened, and Delhi will never be the same.")
        elif self.kavya.stats["corruption"] > 15 or self.kavya.stats["fear"] > 15:
            conclusion_text = ("The first week of Vikram's transformation is complete. "
                              "Kavya is no longer the confident girl she once was. "
                              "The System whispers: 'EXCELLENT PROGRESS. TARGET CORRUPTION ADVANCING.' "
                              "Vikram smiles coldly. This is just the beginning.")
        elif self.kavya.stats["affection"] > 15:
            conclusion_text = ("Vikram has successfully infiltrated Kavya's social circle. "
                              "She now sees him as a friend, maybe even something more. "
                              "The System notes: 'EMOTIONAL MANIPULATION SUCCESSFUL. PROCEED WITH SEDUCTION.' "
                              "Sometimes the sweetest poison is delivered with a smile.")
        else:
            conclusion_text = ("The first phase is complete. Vikram has made contact with his target "
                              "and begun the delicate process of manipulation. "
                              "The System advises: 'PATIENCE, VIKRAM. GREAT POWER REQUIRES CAREFUL PLANNING.' "
                              "Rome wasn't built in a day, and neither is an empire of domination.")

        # Show stats summary
        stats_summary = (f"\n\nKAVYA'S CURRENT STATS:\n"
                        f"Lust: {self.kavya.stats['lust']}/100\n"
                        f"Corruption: {self.kavya.stats['corruption']}/100\n"
                        f"Affection: {self.kavya.stats['affection']}/100\n"
                        f"Fear: {self.kavya.stats['fear']}/100\n"
                        f"Trust: {self.kavya.stats['trust']}/100\n\n"
                        f"VIKRAM'S GROWTH:\n"
                        f"Lust Points Earned: {self.player.lust_points}\n"
                        f"Domination Points Earned: {self.player.domination_points}")

        full_text = conclusion_text + stats_summary

        choices = [
            DialogueChoice(
                "Continue to next chapter...",
                {},
                {},
                {},
                "Aage badhte hain..."
            )
        ]

        self.dialogue_system.start_dialogue(
            "Chapter Conclusion",
            full_text,
            choices,
            callback=lambda choice, target: self.complete_chapter()
        )

    def complete_chapter(self):
        """Complete the chapter"""
        self.player.mood = "Victorious"
        self.next_scene()

    def render(self, screen, font_medium, font_small):
        """Render chapter content"""
        if self.scene_index >= len(self.scenes):
            # Chapter complete
            complete_text = font_medium.render("Chapter 1 Complete!", True, (0, 255, 0))
            complete_rect = complete_text.get_rect(center=(640, 300))
            screen.blit(complete_text, complete_rect)

            # Show final stats
            stats_text = [
                f"Kavya's Corruption: {self.kavya.stats['corruption']}/100",
                f"Kavya's Fear: {self.kavya.stats['fear']}/100",
                f"Kavya's Affection: {self.kavya.stats['affection']}/100",
                f"Lust Points Earned: {self.player.lust_points}",
                f"Domination Points Earned: {self.player.domination_points}"
            ]

            y_offset = 350
            for stat in stats_text:
                stat_surface = font_small.render(stat, True, (255, 255, 255))
                stat_rect = stat_surface.get_rect(center=(640, y_offset))
                screen.blit(stat_surface, stat_rect)
                y_offset += 25

            continue_text = font_small.render("More chapters coming soon... Press ESC for menu", True, (128, 128, 128))
            continue_rect = continue_text.get_rect(center=(640, 500))
            screen.blit(continue_text, continue_rect)
