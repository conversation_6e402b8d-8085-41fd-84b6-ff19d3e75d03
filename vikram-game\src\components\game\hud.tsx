'use client';

import { useGameStore } from '@/lib/stores/game-store';
import { AnimatedCard, AnimatedStatBar } from '@/components/ui/animated-card';
import { getLocationName, getStatColor } from '@/lib/utils';
import { User, MapPin, Clock, Target, Trophy, Settings } from 'lucide-react';

export function GameHUD() {
  const { player, activeQuests } = useGameStore();
  
  return (
    <div className="fixed top-0 left-0 right-0 z-40 bg-black/80 backdrop-blur-sm border-b border-gray-700">
      <div className="flex items-center justify-between px-6 py-3">
        {/* Player Info */}
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <User className="w-5 h-5 text-yellow-400" />
            <span className="text-white font-semibold">{player.name}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <MapPin className="w-4 h-4 text-blue-400" />
            <span className="text-gray-300 text-sm">{getLocationName(player.location)}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-green-400" />
            <span className="text-gray-300 text-sm">{player.time}</span>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className="text-xs text-gray-400">Mood</div>
            <div className="text-sm text-white">{player.mood}</div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-gray-400">Lust Points</div>
            <div className="text-sm text-purple-400 font-semibold">{player.lustPoints}</div>
          </div>
          
          <div className="text-center">
            <div className="text-xs text-gray-400">Domination</div>
            <div className="text-sm text-red-400 font-semibold">{player.dominationPoints}</div>
          </div>
        </div>

        {/* Active Quest */}
        {activeQuests.length > 0 && (
          <div className="flex items-center space-x-2">
            <Target className="w-4 h-4 text-orange-400" />
            <div className="text-right">
              <div className="text-xs text-gray-400">Active Quest</div>
              <div className="text-sm text-orange-400">{activeQuests[0].title}</div>
            </div>
          </div>
        )}

        {/* Menu Button */}
        <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
          <Settings className="w-5 h-5 text-gray-400" />
        </button>
      </div>
    </div>
  );
}

export function StatsPanel() {
  const { player } = useGameStore();
  
  return (
    <AnimatedCard className="w-80">
      <div className="space-y-4">
        <div className="flex items-center space-x-2 mb-4">
          <Trophy className="w-5 h-5 text-yellow-400" />
          <h3 className="text-lg font-semibold text-white">Vikram's Stats</h3>
        </div>
        
        <AnimatedStatBar
          label="Charisma"
          value={player.stats.charisma}
          maxValue={20}
          color="bg-pink-500"
          delay={0}
        />
        
        <AnimatedStatBar
          label="Intelligence"
          value={player.stats.intelligence}
          maxValue={20}
          color="bg-blue-500"
          delay={100}
        />
        
        <AnimatedStatBar
          label="Manipulation"
          value={player.stats.manipulation}
          maxValue={20}
          color="bg-purple-500"
          delay={200}
        />
        
        <AnimatedStatBar
          label="Wealth"
          value={player.stats.wealth}
          maxValue={20}
          color="bg-yellow-500"
          delay={300}
        />
        
        <AnimatedStatBar
          label="Dominance"
          value={player.stats.dominance}
          maxValue={20}
          color="bg-red-500"
          delay={400}
        />
      </div>
    </AnimatedCard>
  );
}
