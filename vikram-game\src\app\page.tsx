'use client';

import { useState } from 'react';
import { useGameStore } from '@/lib/stores/game-store';
import { GameHUD, StatsPanel } from '@/components/game/hud';
import { DialogueSystem } from '@/components/game/dialogue-system';
import { AnimatedCard } from '@/components/ui/animated-card';
import { useSpring, animated } from '@react-spring/web';
import { Play, Settings, BookOpen, Trophy } from 'lucide-react';

type GameScreen = 'menu' | 'game' | 'stats' | 'settings';

export default function Home() {
  const [currentScreen, setCurrentScreen] = useState<GameScreen>('menu');
  const [currentScene, setCurrentScene] = useState('intro');
  const { player, progress, resetGame } = useGameStore();

  const backgroundSpring = useSpring({
    background: currentScreen === 'menu'
      ? 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'
      : 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
    config: { duration: 1000 }
  });

  const handleStartGame = () => {
    setCurrentScreen('game');
    setCurrentScene('intro');
  };

  const handleSceneComplete = () => {
    // Logic to advance to next scene
    if (currentScene === 'intro') {
      setCurrentScene('second_encounter');
    } else {
      // Game complete for now
      setCurrentScreen('menu');
    }
  };

  const renderMenu = () => (
    <div className="min-h-screen flex items-center justify-center p-8">
      <div className="text-center space-y-8 max-w-2xl">
        {/* Title */}
        <div className="space-y-4">
          <h1 className="text-6xl font-bold bg-gradient-to-r from-yellow-400 via-red-500 to-purple-600 bg-clip-text text-transparent">
            VIKRAM
          </h1>
          <h2 className="text-2xl text-gray-300 font-semibold">
            The Delhi Domination System
          </h2>
          <p className="text-lg text-gray-400">
            Chapter 1: School Days - The Serpent's Genesis
          </p>
        </div>

        {/* Menu Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-12">
          <AnimatedCard
            delay={0}
            onClick={handleStartGame}
            className="text-center space-y-3 hover:border-green-500"
          >
            <Play className="w-8 h-8 text-green-400 mx-auto" />
            <h3 className="text-xl font-semibold text-white">Start Game</h3>
            <p className="text-gray-400">Begin Vikram's journey to power</p>
          </AnimatedCard>

          <AnimatedCard
            delay={100}
            onClick={() => setCurrentScreen('stats')}
            className="text-center space-y-3 hover:border-blue-500"
          >
            <Trophy className="w-8 h-8 text-blue-400 mx-auto" />
            <h3 className="text-xl font-semibold text-white">Stats</h3>
            <p className="text-gray-400">View character progression</p>
          </AnimatedCard>

          <AnimatedCard
            delay={200}
            className="text-center space-y-3 hover:border-purple-500"
          >
            <BookOpen className="w-8 h-8 text-purple-400 mx-auto" />
            <h3 className="text-xl font-semibold text-white">Gallery</h3>
            <p className="text-gray-400">Unlocked scenes & achievements</p>
          </AnimatedCard>

          <AnimatedCard
            delay={300}
            onClick={() => setCurrentScreen('settings')}
            className="text-center space-y-3 hover:border-yellow-500"
          >
            <Settings className="w-8 h-8 text-yellow-400 mx-auto" />
            <h3 className="text-xl font-semibold text-white">Settings</h3>
            <p className="text-gray-400">Game preferences</p>
          </AnimatedCard>
        </div>

        {/* Warning */}
        <div className="mt-8 p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
          <p className="text-red-400 text-sm">
            ⚠️ This game contains mature themes and is intended for adults (18+)
          </p>
        </div>
      </div>
    </div>
  );

  const renderGame = () => (
    <div className="min-h-screen relative">
      <GameHUD />

      {/* Game Background */}
      <div className="pt-20 min-h-screen bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900">
        {/* Scene Background */}
        <div className="absolute inset-0 bg-[url('/classroom-bg.jpg')] bg-cover bg-center opacity-20" />

        {/* Game Content */}
        <div className="relative z-10">
          <DialogueSystem
            sceneId={currentScene}
            onSceneComplete={handleSceneComplete}
          />
        </div>
      </div>

      {/* Back to Menu Button */}
      <button
        onClick={() => setCurrentScreen('menu')}
        className="fixed top-4 right-4 z-50 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors"
      >
        Menu
      </button>
    </div>
  );

  const renderStats = () => (
    <div className="min-h-screen p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">Character Stats</h1>
          <button
            onClick={() => setCurrentScreen('menu')}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Back to Menu
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <StatsPanel />

          <AnimatedCard>
            <h3 className="text-xl font-semibold text-white mb-4">Progress</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-300">Current Chapter</span>
                <span className="text-white">{progress.currentChapter}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Lust Points</span>
                <span className="text-purple-400">{player.lustPoints}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Domination Points</span>
                <span className="text-red-400">{player.dominationPoints}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Completed Quests</span>
                <span className="text-green-400">{progress.completedQuests.length}</span>
              </div>
            </div>
          </AnimatedCard>
        </div>
      </div>
    </div>
  );

  return (
    <animated.div style={backgroundSpring} className="min-h-screen">
      {currentScreen === 'menu' && renderMenu()}
      {currentScreen === 'game' && renderGame()}
      {currentScreen === 'stats' && renderStats()}
      {currentScreen === 'settings' && (
        <div className="min-h-screen flex items-center justify-center">
          <AnimatedCard>
            <h2 className="text-2xl font-bold text-white mb-4">Settings</h2>
            <p className="text-gray-400 mb-4">Settings panel coming soon...</p>
            <button
              onClick={() => setCurrentScreen('menu')}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              Back to Menu
            </button>
          </AnimatedCard>
        </div>
      )}
    </animated.div>
  );
}
