'use client';

import { useSpring, animated } from '@react-spring/web';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

interface AnimatedCardProps {
  children: ReactNode;
  className?: string;
  delay?: number;
  hover?: boolean;
  onClick?: () => void;
}

export function AnimatedCard({ 
  children, 
  className, 
  delay = 0, 
  hover = true,
  onClick 
}: AnimatedCardProps) {
  const [springs, api] = useSpring(() => ({
    from: { 
      opacity: 0, 
      transform: 'translateY(20px) scale(0.95)' 
    },
    to: { 
      opacity: 1, 
      transform: 'translateY(0px) scale(1)' 
    },
    delay,
    config: { tension: 280, friction: 60 }
  }));

  const [hoverSprings, hoverApi] = useSpring(() => ({
    transform: 'scale(1)',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    config: { tension: 300, friction: 30 }
  }));

  const handleMouseEnter = () => {
    if (hover) {
      hoverApi.start({
        transform: 'scale(1.02)',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      });
    }
  };

  const handleMouseLeave = () => {
    if (hover) {
      hoverApi.start({
        transform: 'scale(1)',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      });
    }
  };

  return (
    <animated.div
      style={{ ...springs, ...hoverSprings }}
      className={cn(
        'bg-gray-900/90 backdrop-blur-sm border border-gray-700 rounded-lg p-6',
        hover && 'cursor-pointer transition-colors hover:border-gray-600',
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
    >
      {children}
    </animated.div>
  );
}

interface StatBarProps {
  label: string;
  value: number;
  maxValue?: number;
  color?: string;
  delay?: number;
}

export function AnimatedStatBar({ 
  label, 
  value, 
  maxValue = 100, 
  color = 'bg-blue-500',
  delay = 0 
}: StatBarProps) {
  const percentage = Math.min((value / maxValue) * 100, 100);
  
  const barSpring = useSpring({
    width: `${percentage}%`,
    from: { width: '0%' },
    delay,
    config: { tension: 280, friction: 60 }
  });

  const textSpring = useSpring({
    opacity: 1,
    from: { opacity: 0 },
    delay: delay + 200,
  });

  return (
    <div className="space-y-2">
      <animated.div style={textSpring} className="flex justify-between text-sm">
        <span className="text-gray-300">{label}</span>
        <span className="text-gray-400">{value}/{maxValue}</span>
      </animated.div>
      <div className="w-full bg-gray-700 rounded-full h-2">
        <animated.div
          style={barSpring}
          className={cn('h-2 rounded-full transition-colors', color)}
        />
      </div>
    </div>
  );
}
