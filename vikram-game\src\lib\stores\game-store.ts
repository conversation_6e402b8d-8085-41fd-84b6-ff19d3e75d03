import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { GameState, PlayerStats, TargetStats, Character, Quest, DialogueChoice } from '../types';

interface GameStore extends GameState {
  // Player actions
  updatePlayerStats: (stats: Partial<PlayerStats>) => void;
  addLustPoints: (points: number) => void;
  addDominationPoints: (points: number) => void;
  setPlayerLocation: (location: GameState['player']['location']) => void;
  setPlayerMood: (mood: string) => void;
  setPlayerTime: (time: string) => void;
  
  // Character actions
  updateCharacterStats: (characterId: string, stats: Partial<TargetStats>) => void;
  revealCharacterProfile: (characterId: string) => void;
  dominateCharacter: (characterId: string) => void;
  updateCharacterRelationship: (characterId: string, relationship: Character['relationship']) => void;
  
  // Dialogue actions
  startDialogue: (nodeId: string, characterId?: string, sceneId?: string) => void;
  endDialogue: () => void;
  makeChoice: (choice: DialogueChoice, targetCharacterId?: string) => void;
  
  // Quest actions
  addQuest: (quest: Quest) => void;
  completeQuest: (questId: string) => void;
  updateQuestObjectives: (questId: string, objectives: string[]) => void;
  
  // Progress actions
  unlockScene: (sceneId: string) => void;
  setCurrentScene: (sceneId: string) => void;
  addAchievement: (achievementId: string) => void;
  
  // Settings actions
  updateSettings: (settings: Partial<GameState['settings']>) => void;
  
  // Game actions
  resetGame: () => void;
  saveGame: () => void;
}

const initialGameState: GameState = {
  player: {
    name: 'Vikram Singh',
    stats: {
      charisma: 5,
      intelligence: 7,
      manipulation: 3,
      wealth: 2,
      dominance: 1,
    },
    lustPoints: 0,
    dominationPoints: 0,
    inventory: [],
    location: 'dps_classroom',
    mood: 'Calculating',
    time: '10:30 AM',
  },
  characters: {
    kavya: {
      id: 'kavya',
      name: 'Kavya Gupta',
      description: 'Popular girl in class, thinks she\'s untouchable',
      stats: {
        lust: 0,
        corruption: 0,
        affection: 0,
        fear: 0,
        trust: 0,
      },
      secrets: ['Has a crush on the cricket captain', 'Cheated on last exam'],
      dominated: false,
      profileRevealed: false,
      relationship: 'stranger',
    },
  },
  progress: {
    currentChapter: 1,
    currentScene: 'intro',
    unlockedScenes: ['intro'],
    completedQuests: [],
    discoveredSecrets: [],
    achievements: [],
  },
  settings: {
    textSpeed: 50,
    autoAdvance: false,
    skipSeen: false,
    volume: {
      master: 80,
      music: 70,
      sfx: 80,
      voice: 90,
    },
  },
  activeQuests: [],
  shop: [],
};

export const useGameStore = create<GameStore>()(
  persist(
    (set, get) => ({
      ...initialGameState,
      
      // Player actions
      updatePlayerStats: (stats) =>
        set((state) => ({
          player: {
            ...state.player,
            stats: { ...state.player.stats, ...stats },
          },
        })),
      
      addLustPoints: (points) =>
        set((state) => ({
          player: {
            ...state.player,
            lustPoints: state.player.lustPoints + points,
          },
        })),
      
      addDominationPoints: (points) =>
        set((state) => ({
          player: {
            ...state.player,
            dominationPoints: state.player.dominationPoints + points,
          },
        })),
      
      setPlayerLocation: (location) =>
        set((state) => ({
          player: { ...state.player, location },
        })),
      
      setPlayerMood: (mood) =>
        set((state) => ({
          player: { ...state.player, mood },
        })),
      
      setPlayerTime: (time) =>
        set((state) => ({
          player: { ...state.player, time },
        })),
      
      // Character actions
      updateCharacterStats: (characterId, stats) =>
        set((state) => ({
          characters: {
            ...state.characters,
            [characterId]: {
              ...state.characters[characterId],
              stats: { ...state.characters[characterId].stats, ...stats },
            },
          },
        })),
      
      revealCharacterProfile: (characterId) =>
        set((state) => ({
          characters: {
            ...state.characters,
            [characterId]: {
              ...state.characters[characterId],
              profileRevealed: true,
            },
          },
        })),
      
      dominateCharacter: (characterId) =>
        set((state) => ({
          characters: {
            ...state.characters,
            [characterId]: {
              ...state.characters[characterId],
              dominated: true,
              relationship: 'dominated',
            },
          },
        })),
      
      updateCharacterRelationship: (characterId, relationship) =>
        set((state) => ({
          characters: {
            ...state.characters,
            [characterId]: {
              ...state.characters[characterId],
              relationship,
            },
          },
        })),
      
      // Dialogue actions
      startDialogue: (nodeId, characterId, sceneId = 'current') =>
        set(() => ({
          currentDialogue: { nodeId, characterId, sceneId },
        })),
      
      endDialogue: () =>
        set(() => ({
          currentDialogue: undefined,
        })),
      
      makeChoice: (choice, targetCharacterId) => {
        const state = get();
        
        // Apply player stat effects
        if (Object.keys(choice.playerStatEffects).length > 0) {
          state.updatePlayerStats(choice.playerStatEffects);
        }
        
        // Apply target stat effects
        if (targetCharacterId && Object.keys(choice.targetStatEffects).length > 0) {
          state.updateCharacterStats(targetCharacterId, choice.targetStatEffects);
        }
      },
      
      // Quest actions
      addQuest: (quest) =>
        set((state) => ({
          activeQuests: [...state.activeQuests, quest],
        })),
      
      completeQuest: (questId) =>
        set((state) => ({
          activeQuests: state.activeQuests.map((quest) =>
            quest.id === questId ? { ...quest, completed: true, active: false } : quest
          ),
          progress: {
            ...state.progress,
            completedQuests: [...state.progress.completedQuests, questId],
          },
        })),
      
      updateQuestObjectives: (questId, objectives) =>
        set((state) => ({
          activeQuests: state.activeQuests.map((quest) =>
            quest.id === questId ? { ...quest, objectives } : quest
          ),
        })),
      
      // Progress actions
      unlockScene: (sceneId) =>
        set((state) => ({
          progress: {
            ...state.progress,
            unlockedScenes: [...new Set([...state.progress.unlockedScenes, sceneId])],
          },
        })),
      
      setCurrentScene: (sceneId) =>
        set((state) => ({
          progress: { ...state.progress, currentScene: sceneId },
        })),
      
      addAchievement: (achievementId) =>
        set((state) => ({
          progress: {
            ...state.progress,
            achievements: [...new Set([...state.progress.achievements, achievementId])],
          },
        })),
      
      // Settings actions
      updateSettings: (settings) =>
        set((state) => ({
          settings: { ...state.settings, ...settings },
        })),
      
      // Game actions
      resetGame: () => set(() => ({ ...initialGameState })),
      
      saveGame: () => {
        // This will be handled by the persist middleware
        console.log('Game saved!');
      },
    }),
    {
      name: 'vikram-game-storage',
      partialize: (state) => ({
        player: state.player,
        characters: state.characters,
        progress: state.progress,
        settings: state.settings,
        activeQuests: state.activeQuests,
      }),
    }
  )
);
