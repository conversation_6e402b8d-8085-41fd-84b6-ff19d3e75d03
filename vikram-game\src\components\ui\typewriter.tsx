'use client';

import { useState, useEffect } from 'react';
import { useSpring, animated } from '@react-spring/web';

interface TypewriterProps {
  text: string;
  speed?: number;
  delay?: number;
  onComplete?: () => void;
  className?: string;
}

export function Typewriter({ 
  text, 
  speed = 50, 
  delay = 0, 
  onComplete,
  className = '' 
}: TypewriterProps) {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  const cursorSpring = useSpring({
    opacity: isComplete ? 0 : 1,
    from: { opacity: 0 },
    loop: !isComplete,
    config: { duration: 500 }
  });

  useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayedText(text.slice(0, currentIndex + 1));
        setCurrentIndex(currentIndex + 1);
      }, delay + speed);

      return () => clearTimeout(timer);
    } else if (!isComplete) {
      setIsComplete(true);
      onComplete?.();
    }
  }, [currentIndex, text, speed, delay, isComplete, onComplete]);

  // Reset when text changes
  useEffect(() => {
    setDisplayedText('');
    setCurrentIndex(0);
    setIsComplete(false);
  }, [text]);

  return (
    <div className={className}>
      <span>{displayedText}</span>
      <animated.span style={cursorSpring} className="text-blue-400">
        |
      </animated.span>
    </div>
  );
}

interface DialogueBoxProps {
  speaker: string;
  text: string;
  onComplete?: () => void;
  className?: string;
}

export function DialogueBox({ 
  speaker, 
  text, 
  onComplete,
  className = '' 
}: DialogueBoxProps) {
  const containerSpring = useSpring({
    opacity: 1,
    transform: 'translateY(0px)',
    from: { opacity: 0, transform: 'translateY(20px)' },
    config: { tension: 280, friction: 60 }
  });

  return (
    <animated.div 
      style={containerSpring}
      className={`bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg p-6 ${className}`}
    >
      <div className="mb-3">
        <h3 className="text-lg font-semibold text-yellow-400">{speaker}</h3>
      </div>
      <div className="text-gray-100 leading-relaxed">
        <Typewriter 
          text={text} 
          speed={30} 
          onComplete={onComplete}
          className="text-base"
        />
      </div>
    </animated.div>
  );
}
