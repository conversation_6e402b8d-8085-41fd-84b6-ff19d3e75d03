/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cidlescaper%5Cvikram-game%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidlescaper%5Cvikram-game&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cidlescaper%5Cvikram-game%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidlescaper%5Cvikram-game&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cidlescaper%5Cvikram-game%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidlescaper%5Cvikram-game&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNpZGxlc2NhcGVyJTVDJTVDdmlrcmFtLWdhbWUlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQW9GIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxpZGxlc2NhcGVyXFxcXHZpa3JhbS1nYW1lXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxpZGxlc2NhcGVyXFx2aWtyYW0tZ2FtZVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"37c3f8f6745c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcaWRsZXNjYXBlclxcdmlrcmFtLWdhbWVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjM3YzNmOGY2NzQ1Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkQ6XFxpZGxlc2NhcGVyXFx2aWtyYW0tZ2FtZVxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNyZWF0ZSBOZXh0IEFwcFwiLFxuICBkZXNjcmlwdGlvbjogXCJHZW5lcmF0ZWQgYnkgY3JlYXRlIG5leHQgYXBwXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\idlescaper\\vikram-game\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNpZGxlc2NhcGVyJTVDJTVDdmlrcmFtLWdhbWUlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQW9GIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxpZGxlc2NhcGVyXFxcXHZpa3JhbS1nYW1lXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidlescaper%5C%5Cvikram-game%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_stores_game_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/game-store */ \"(ssr)/./src/lib/stores/game-store.ts\");\n/* harmony import */ var _components_game_hud__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/game/hud */ \"(ssr)/./src/components/game/hud.tsx\");\n/* harmony import */ var _components_game_dialogue_system__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/game/dialogue-system */ \"(ssr)/./src/components/game/dialogue-system.tsx\");\n/* harmony import */ var _components_ui_animated_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/animated-card */ \"(ssr)/./src/components/ui/animated-card.tsx\");\n/* harmony import */ var _react_spring_web__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @react-spring/web */ \"(ssr)/./node_modules/@react-spring/web/dist/react-spring_web.modern.mjs\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Play_Settings_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Play,Settings,Trophy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Play_Settings_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Play,Settings,Trophy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Play_Settings_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Play,Settings,Trophy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Play_Settings_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Play,Settings,Trophy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Home() {\n    const [currentScreen, setCurrentScreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('menu');\n    const [currentScene, setCurrentScene] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('intro');\n    const { player, progress, resetGame } = (0,_lib_stores_game_store__WEBPACK_IMPORTED_MODULE_2__.useGameStore)();\n    const backgroundSpring = (0,_react_spring_web__WEBPACK_IMPORTED_MODULE_6__.useSpring)({\n        background: currentScreen === 'menu' ? 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)' : 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',\n        config: {\n            duration: 1000\n        }\n    });\n    const handleStartGame = ()=>{\n        setCurrentScreen('game');\n        setCurrentScene('intro');\n    };\n    const handleSceneComplete = ()=>{\n        // Logic to advance to next scene\n        if (currentScene === 'intro') {\n            setCurrentScene('second_encounter');\n        } else {\n            // Game complete for now\n            setCurrentScreen('menu');\n        }\n    };\n    const renderMenu = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-8 max-w-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold bg-gradient-to-r from-yellow-400 via-red-500 to-purple-600 bg-clip-text text-transparent\",\n                                children: \"VIKRAM\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl text-gray-300 font-semibold\",\n                                children: \"The Delhi Domination System\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-400\",\n                                children: \"Chapter 1: School Days - The Serpent's Genesis\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_5__.AnimatedCard, {\n                                delay: 0,\n                                onClick: handleStartGame,\n                                className: \"text-center space-y-3 hover:border-green-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Play_Settings_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-8 h-8 text-green-400 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Start Game\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Begin Vikram's journey to power\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_5__.AnimatedCard, {\n                                delay: 100,\n                                onClick: ()=>setCurrentScreen('stats'),\n                                className: \"text-center space-y-3 hover:border-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Play_Settings_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-400 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Stats\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"View character progression\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_5__.AnimatedCard, {\n                                delay: 200,\n                                className: \"text-center space-y-3 hover:border-purple-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Play_Settings_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-8 h-8 text-purple-400 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Gallery\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Unlocked scenes & achievements\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_5__.AnimatedCard, {\n                                delay: 300,\n                                onClick: ()=>setCurrentScreen('settings'),\n                                className: \"text-center space-y-3 hover:border-yellow-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Play_Settings_Trophy_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-8 h-8 text-yellow-400 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white\",\n                                        children: \"Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Game preferences\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 p-4 bg-red-900/20 border border-red-500/30 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-400 text-sm\",\n                            children: \"⚠️ This game contains mature themes and is intended for adults (18+)\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 41,\n            columnNumber: 5\n        }, this);\n    const renderGame = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_hud__WEBPACK_IMPORTED_MODULE_3__.GameHUD, {}, void 0, false, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-20 min-h-screen bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-[url('/classroom-bg.jpg')] bg-cover bg-center opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_dialogue_system__WEBPACK_IMPORTED_MODULE_4__.DialogueSystem, {\n                                sceneId: currentScene,\n                                onSceneComplete: handleSceneComplete\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setCurrentScreen('menu'),\n                    className: \"fixed top-4 right-4 z-50 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors\",\n                    children: \"Menu\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 109,\n            columnNumber: 5\n        }, this);\n    const renderStats = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-white mb-4\",\n                                children: \"Character Stats\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentScreen('menu'),\n                                className: \"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                                children: \"Back to Menu\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_game_hud__WEBPACK_IMPORTED_MODULE_3__.StatsPanel, {}, void 0, false, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_5__.AnimatedCard, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-4\",\n                                        children: \"Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"Current Chapter\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: progress.currentChapter\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"Lust Points\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-400\",\n                                                        children: player.lustPoints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"Domination Points\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-400\",\n                                                        children: player.dominationPoints\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"Completed Quests\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: progress.completedQuests.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 137,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_spring_web__WEBPACK_IMPORTED_MODULE_6__.animated.div, {\n        style: backgroundSpring,\n        className: \"min-h-screen\",\n        children: [\n            currentScreen === 'menu' && renderMenu(),\n            currentScreen === 'game' && renderGame(),\n            currentScreen === 'stats' && renderStats(),\n            currentScreen === 'settings' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_5__.AnimatedCard, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-4\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-4\",\n                            children: \"Settings panel coming soon...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setCurrentScreen('menu'),\n                            className: \"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\",\n                            children: \"Back to Menu\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/game/dialogue-system.tsx":
/*!*************************************************!*\
  !*** ./src/components/game/dialogue-system.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DialogueSystem: () => (/* binding */ DialogueSystem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_stores_game_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/game-store */ \"(ssr)/./src/lib/stores/game-store.ts\");\n/* harmony import */ var _components_ui_typewriter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/typewriter */ \"(ssr)/./src/components/ui/typewriter.tsx\");\n/* harmony import */ var _components_ui_animated_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/animated-card */ \"(ssr)/./src/components/ui/animated-card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_data_chapter1__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/data/chapter1 */ \"(ssr)/./src/lib/data/chapter1.ts\");\n/* harmony import */ var _react_spring_web__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @react-spring/web */ \"(ssr)/./node_modules/@react-spring/web/dist/react-spring_web.modern.mjs\");\n/* __next_internal_client_entry_do_not_use__ DialogueSystem auto */ \n\n\n\n\n\n\n\nfunction DialogueSystem({ sceneId, onSceneComplete }) {\n    const { player, characters, currentDialogue, makeChoice, startDialogue, endDialogue, updatePlayerStats, updateCharacterStats, setPlayerMood } = (0,_lib_stores_game_store__WEBPACK_IMPORTED_MODULE_2__.useGameStore)();\n    const [currentNodeIndex, setCurrentNodeIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showChoices, setShowChoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dialogueComplete, setDialogueComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Find the current scene\n    const scene = _lib_data_chapter1__WEBPACK_IMPORTED_MODULE_6__.chapter1Scenes.find((s)=>s.id === sceneId);\n    const currentNode = scene?.dialogueTree[currentNodeIndex];\n    const choicesSpring = (0,_react_spring_web__WEBPACK_IMPORTED_MODULE_7__.useSpring)({\n        opacity: showChoices ? 1 : 0,\n        transform: showChoices ? 'translateY(0px)' : 'translateY(20px)',\n        config: {\n            tension: 280,\n            friction: 60\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DialogueSystem.useEffect\": ()=>{\n            if (scene && !currentDialogue) {\n                startDialogue(scene.dialogueTree[0].id, undefined, sceneId);\n            }\n        }\n    }[\"DialogueSystem.useEffect\"], [\n        scene,\n        currentDialogue,\n        startDialogue,\n        sceneId\n    ]);\n    const handleDialogueComplete = ()=>{\n        if (currentNode?.choices && currentNode.choices.length > 0) {\n            setShowChoices(true);\n        } else if (currentNode?.autoAdvance && currentNode.nextNode) {\n            // Auto advance to next node\n            const nextNodeIndex = scene?.dialogueTree.findIndex((node)=>node.id === currentNode.nextNode);\n            if (nextNodeIndex !== undefined && nextNodeIndex !== -1) {\n                setCurrentNodeIndex(nextNodeIndex);\n                setShowChoices(false);\n            }\n        } else {\n            // End of dialogue\n            setDialogueComplete(true);\n            endDialogue();\n            onSceneComplete?.();\n        }\n    };\n    const handleChoiceSelect = (choice)=>{\n        // Apply choice effects\n        makeChoice(choice, scene?.characters[0]);\n        // Update mood based on choice type\n        if (choice.playerStatEffects.manipulation) {\n            setPlayerMood('Manipulative');\n        } else if (choice.playerStatEffects.dominance) {\n            setPlayerMood('Dominant');\n        } else if (choice.playerStatEffects.charisma) {\n            setPlayerMood('Charming');\n        }\n        // Find next node\n        if (currentNode?.nextNode) {\n            const nextNodeIndex = scene?.dialogueTree.findIndex((node)=>node.id === currentNode.nextNode);\n            if (nextNodeIndex !== undefined && nextNodeIndex !== -1) {\n                setCurrentNodeIndex(nextNodeIndex);\n                setShowChoices(false);\n            }\n        } else {\n            // End of dialogue\n            setDialogueComplete(true);\n            endDialogue();\n            onSceneComplete?.();\n        }\n    };\n    if (!scene || !currentNode || dialogueComplete) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-end justify-center p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black/50 backdrop-blur-sm\"\n            }, void 0, false, {\n                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full max-w-4xl space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typewriter__WEBPACK_IMPORTED_MODULE_3__.DialogueBox, {\n                        speaker: currentNode.speaker,\n                        text: currentNode.text,\n                        onComplete: handleDialogueComplete,\n                        className: \"w-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    showChoices && currentNode.choices && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_spring_web__WEBPACK_IMPORTED_MODULE_7__.animated.div, {\n                        style: choicesSpring,\n                        className: \"space-y-3\",\n                        children: currentNode.choices.map((choice, index)=>{\n                            const canSelect = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.canMakeChoice)(choice.requirements, player.stats);\n                            const targetCharacter = scene.characters[0] ? characters[scene.characters[0]] : null;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_4__.AnimatedCard, {\n                                delay: index * 100,\n                                hover: canSelect,\n                                onClick: canSelect ? ()=>handleChoiceSelect(choice) : undefined,\n                                className: `\n                    ${canSelect ? 'border-blue-500/50 hover:border-blue-400 cursor-pointer' : 'border-gray-600 opacity-50 cursor-not-allowed'}\n                  `,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-medium\",\n                                                    children: choice.hinglishText\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400 ml-4\",\n                                                    children: [\n                                                        \"#\",\n                                                        index + 1\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 italic\",\n                                            children: choice.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 21\n                                        }, this),\n                                        !canSelect && Object.keys(choice.requirements).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-red-400\",\n                                            children: [\n                                                \"Requires: \",\n                                                Object.entries(choice.requirements).map(([stat, value])=>`${stat} ${value}`).join(', ')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 23\n                                        }, this),\n                                        canSelect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4 text-xs\",\n                                            children: [\n                                                Object.entries(choice.playerStatEffects).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-400\",\n                                                    children: [\n                                                        \"Player: \",\n                                                        Object.entries(choice.playerStatEffects).map(([stat, value])=>`${stat} ${value > 0 ? '+' : ''}${value}`).join(', ')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 27\n                                                }, this),\n                                                Object.entries(choice.targetStatEffects).length > 0 && targetCharacter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400\",\n                                                    children: [\n                                                        targetCharacter.name,\n                                                        \": \",\n                                                        Object.entries(choice.targetStatEffects).map(([stat, value])=>`${stat} ${value > 0 ? '+' : ''}${value}`).join(', ')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 19\n                                }, this)\n                            }, choice.id, false, {\n                                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\dialogue-system.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/game/dialogue-system.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/game/hud.tsx":
/*!*************************************!*\
  !*** ./src/components/game/hud.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GameHUD: () => (/* binding */ GameHUD),\n/* harmony export */   StatsPanel: () => (/* binding */ StatsPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_stores_game_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stores/game-store */ \"(ssr)/./src/lib/stores/game-store.ts\");\n/* harmony import */ var _components_ui_animated_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/animated-card */ \"(ssr)/./src/components/ui/animated-card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,MapPin,Settings,Target,Trophy,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,MapPin,Settings,Target,Trophy,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,MapPin,Settings,Target,Trophy,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,MapPin,Settings,Target,Trophy,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,MapPin,Settings,Target,Trophy,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,MapPin,Settings,Target,Trophy,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* __next_internal_client_entry_do_not_use__ GameHUD,StatsPanel auto */ \n\n\n\n\nfunction GameHUD() {\n    const { player, activeQuests } = (0,_lib_stores_game_store__WEBPACK_IMPORTED_MODULE_1__.useGameStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-0 left-0 right-0 z-40 bg-black/80 backdrop-blur-sm border-b border-gray-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between px-6 py-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-yellow-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-semibold\",\n                                    children: player.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-300 text-sm\",\n                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.getLocationName)(player.location)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 text-green-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-300 text-sm\",\n                                    children: player.time\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Mood\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-white\",\n                                    children: player.mood\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Lust Points\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-purple-400 font-semibold\",\n                                    children: player.lustPoints\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Domination\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-red-400 font-semibold\",\n                                    children: player.dominationPoints\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                activeQuests.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-4 h-4 text-orange-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Active Quest\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-orange-400\",\n                                    children: activeQuests[0].title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"p-2 hover:bg-gray-700 rounded-lg transition-colors\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-5 h-5 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\nfunction StatsPanel() {\n    const { player } = (0,_lib_stores_game_store__WEBPACK_IMPORTED_MODULE_1__.useGameStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_2__.AnimatedCard, {\n        className: \"w-80\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_MapPin_Settings_Target_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-5 h-5 text-yellow-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-white\",\n                            children: \"Vikram's Stats\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_2__.AnimatedStatBar, {\n                    label: \"Charisma\",\n                    value: player.stats.charisma,\n                    maxValue: 20,\n                    color: \"bg-pink-500\",\n                    delay: 0\n                }, void 0, false, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_2__.AnimatedStatBar, {\n                    label: \"Intelligence\",\n                    value: player.stats.intelligence,\n                    maxValue: 20,\n                    color: \"bg-blue-500\",\n                    delay: 100\n                }, void 0, false, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_2__.AnimatedStatBar, {\n                    label: \"Manipulation\",\n                    value: player.stats.manipulation,\n                    maxValue: 20,\n                    color: \"bg-purple-500\",\n                    delay: 200\n                }, void 0, false, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_2__.AnimatedStatBar, {\n                    label: \"Wealth\",\n                    value: player.stats.wealth,\n                    maxValue: 20,\n                    color: \"bg-yellow-500\",\n                    delay: 300\n                }, void 0, false, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_card__WEBPACK_IMPORTED_MODULE_2__.AnimatedStatBar, {\n                    label: \"Dominance\",\n                    value: player.stats.dominance,\n                    maxValue: 20,\n                    color: \"bg-red-500\",\n                    delay: 400\n                }, void 0, false, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\game\\\\hud.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/game/hud.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/animated-card.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/animated-card.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedCard: () => (/* binding */ AnimatedCard),\n/* harmony export */   AnimatedStatBar: () => (/* binding */ AnimatedStatBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_spring_web__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-spring/web */ \"(ssr)/./node_modules/@react-spring/web/dist/react-spring_web.modern.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ AnimatedCard,AnimatedStatBar auto */ \n\n\nfunction AnimatedCard({ children, className, delay = 0, hover = true, onClick }) {\n    const [springs, api] = (0,_react_spring_web__WEBPACK_IMPORTED_MODULE_1__.useSpring)({\n        \"AnimatedCard.useSpring\": ()=>({\n                from: {\n                    opacity: 0,\n                    transform: 'translateY(20px) scale(0.95)'\n                },\n                to: {\n                    opacity: 1,\n                    transform: 'translateY(0px) scale(1)'\n                },\n                delay,\n                config: {\n                    tension: 280,\n                    friction: 60\n                }\n            })\n    }[\"AnimatedCard.useSpring\"]);\n    const [hoverSprings, hoverApi] = (0,_react_spring_web__WEBPACK_IMPORTED_MODULE_1__.useSpring)({\n        \"AnimatedCard.useSpring\": ()=>({\n                transform: 'scale(1)',\n                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n                config: {\n                    tension: 300,\n                    friction: 30\n                }\n            })\n    }[\"AnimatedCard.useSpring\"]);\n    const handleMouseEnter = ()=>{\n        if (hover) {\n            hoverApi.start({\n                transform: 'scale(1.02)',\n                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'\n            });\n        }\n    };\n    const handleMouseLeave = ()=>{\n        if (hover) {\n            hoverApi.start({\n                transform: 'scale(1)',\n                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_spring_web__WEBPACK_IMPORTED_MODULE_1__.animated.div, {\n        style: {\n            ...springs,\n            ...hoverSprings\n        },\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('bg-gray-900/90 backdrop-blur-sm border border-gray-700 rounded-lg p-6', hover && 'cursor-pointer transition-colors hover:border-gray-600', className),\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        onClick: onClick,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\animated-card.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatedStatBar({ label, value, maxValue = 100, color = 'bg-blue-500', delay = 0 }) {\n    const percentage = Math.min(value / maxValue * 100, 100);\n    const barSpring = (0,_react_spring_web__WEBPACK_IMPORTED_MODULE_1__.useSpring)({\n        width: `${percentage}%`,\n        from: {\n            width: '0%'\n        },\n        delay,\n        config: {\n            tension: 280,\n            friction: 60\n        }\n    });\n    const textSpring = (0,_react_spring_web__WEBPACK_IMPORTED_MODULE_1__.useSpring)({\n        opacity: 1,\n        from: {\n            opacity: 0\n        },\n        delay: delay + 200\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_spring_web__WEBPACK_IMPORTED_MODULE_1__.animated.div, {\n                style: textSpring,\n                className: \"flex justify-between text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-300\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\animated-card.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: [\n                            value,\n                            \"/\",\n                            maxValue\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\animated-card.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\animated-card.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-gray-700 rounded-full h-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_spring_web__WEBPACK_IMPORTED_MODULE_1__.animated.div, {\n                    style: barSpring,\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('h-2 rounded-full transition-colors', color)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\animated-card.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\animated-card.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\animated-card.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/animated-card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/typewriter.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/typewriter.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DialogueBox: () => (/* binding */ DialogueBox),\n/* harmony export */   Typewriter: () => (/* binding */ Typewriter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_spring_web__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-spring/web */ \"(ssr)/./node_modules/@react-spring/web/dist/react-spring_web.modern.mjs\");\n/* __next_internal_client_entry_do_not_use__ Typewriter,DialogueBox auto */ \n\n\nfunction Typewriter({ text, speed = 50, delay = 0, onComplete, className = '' }) {\n    const [displayedText, setDisplayedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isComplete, setIsComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const cursorSpring = (0,_react_spring_web__WEBPACK_IMPORTED_MODULE_2__.useSpring)({\n        opacity: isComplete ? 0 : 1,\n        from: {\n            opacity: 0\n        },\n        loop: !isComplete,\n        config: {\n            duration: 500\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Typewriter.useEffect\": ()=>{\n            if (currentIndex < text.length) {\n                const timer = setTimeout({\n                    \"Typewriter.useEffect.timer\": ()=>{\n                        setDisplayedText(text.slice(0, currentIndex + 1));\n                        setCurrentIndex(currentIndex + 1);\n                    }\n                }[\"Typewriter.useEffect.timer\"], delay + speed);\n                return ({\n                    \"Typewriter.useEffect\": ()=>clearTimeout(timer)\n                })[\"Typewriter.useEffect\"];\n            } else if (!isComplete) {\n                setIsComplete(true);\n                onComplete?.();\n            }\n        }\n    }[\"Typewriter.useEffect\"], [\n        currentIndex,\n        text,\n        speed,\n        delay,\n        isComplete,\n        onComplete\n    ]);\n    // Reset when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Typewriter.useEffect\": ()=>{\n            setDisplayedText('');\n            setCurrentIndex(0);\n            setIsComplete(false);\n        }\n    }[\"Typewriter.useEffect\"], [\n        text\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: displayedText\n            }, void 0, false, {\n                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\typewriter.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_spring_web__WEBPACK_IMPORTED_MODULE_2__.animated.span, {\n                style: cursorSpring,\n                className: \"text-blue-400\",\n                children: \"|\"\n            }, void 0, false, {\n                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\typewriter.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\typewriter.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction DialogueBox({ speaker, text, onComplete, className = '' }) {\n    const containerSpring = (0,_react_spring_web__WEBPACK_IMPORTED_MODULE_2__.useSpring)({\n        opacity: 1,\n        transform: 'translateY(0px)',\n        from: {\n            opacity: 0,\n            transform: 'translateY(20px)'\n        },\n        config: {\n            tension: 280,\n            friction: 60\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_spring_web__WEBPACK_IMPORTED_MODULE_2__.animated.div, {\n        style: containerSpring,\n        className: `bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg p-6 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-yellow-400\",\n                    children: speaker\n                }, void 0, false, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\typewriter.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\typewriter.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-100 leading-relaxed\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Typewriter, {\n                    text: text,\n                    speed: 30,\n                    onComplete: onComplete,\n                    className: \"text-base\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\typewriter.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\typewriter.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idlescaper\\\\vikram-game\\\\src\\\\components\\\\ui\\\\typewriter.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/typewriter.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/data/chapter1.ts":
/*!**********************************!*\
  !*** ./src/lib/data/chapter1.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chapter1Quests: () => (/* binding */ chapter1Quests),\n/* harmony export */   chapter1Scenes: () => (/* binding */ chapter1Scenes)\n/* harmony export */ });\nconst chapter1Scenes = [\n    {\n        id: 'intro',\n        title: 'The Serpent\\'s Genesis',\n        description: 'Vikram\\'s story begins in the halls of Delhi Public School',\n        background: 'dps_classroom',\n        characters: [],\n        dialogueTree: [\n            {\n                id: 'intro_1',\n                speaker: 'Narrator',\n                text: 'Vikram Singh sits in the back corner of his Delhi Public School classroom, staring blankly at the blackboard. Three months since his father\\'s death, and the world still feels hollow. But today... today something is different.',\n                choices: [\n                    {\n                        id: 'intro_continue',\n                        text: 'Continue...',\n                        hinglishText: 'Aage badhte hain...',\n                        playerStatEffects: {},\n                        targetStatEffects: {},\n                        requirements: {}\n                    }\n                ],\n                autoAdvance: false,\n                nextNode: 'system_awakening'\n            },\n            {\n                id: 'system_awakening',\n                speaker: 'System',\n                text: 'Suddenly, a translucent blue interface materializes before Vikram\\'s eyes. Text scrolls across his vision: \"DELHI DOMINATION SYSTEM ACTIVATED. WELCOME, VIKRAM SINGH. YOUR JOURNEY TO ABSOLUTE POWER BEGINS NOW.\"',\n                choices: [\n                    {\n                        id: 'system_wtf',\n                        text: 'What the hell is this?',\n                        hinglishText: 'Yeh kya bakchodi hai?',\n                        playerStatEffects: {\n                            intelligence: 1\n                        },\n                        targetStatEffects: {},\n                        requirements: {}\n                    },\n                    {\n                        id: 'system_crazy',\n                        text: 'Am I going crazy?',\n                        hinglishText: 'Kya main pagal ho gaya hun?',\n                        playerStatEffects: {\n                            intelligence: 1\n                        },\n                        targetStatEffects: {},\n                        requirements: {}\n                    }\n                ],\n                nextNode: 'target_identification'\n            },\n            {\n                id: 'target_identification',\n                speaker: 'Narrator',\n                text: 'Vikram\\'s gaze falls on Kavya Gupta, the class princess. She\\'s laughing with her friends, completely oblivious to his existence. The System interface pulses: \"TARGET IDENTIFIED. SCAN AVAILABLE.\"',\n                choices: [\n                    {\n                        id: 'scan_kavya',\n                        text: 'Scan Kavya',\n                        hinglishText: 'Kavya ko scan karo',\n                        playerStatEffects: {\n                            intelligence: 1\n                        },\n                        targetStatEffects: {},\n                        requirements: {},\n                        unlocks: [\n                            'kavya_profile'\n                        ]\n                    },\n                    {\n                        id: 'ignore_system',\n                        text: 'Ignore the System',\n                        hinglishText: 'System ko ignore karo',\n                        playerStatEffects: {},\n                        targetStatEffects: {},\n                        requirements: {}\n                    }\n                ],\n                nextNode: 'first_approach'\n            },\n            {\n                id: 'first_approach',\n                speaker: 'Narrator',\n                text: 'During the break, Vikram approaches Kavya\\'s desk. She looks up, surprised that the \\'invisible boy\\' is talking to her.',\n                choices: [],\n                autoAdvance: true,\n                nextNode: 'kavya_response'\n            },\n            {\n                id: 'kavya_response',\n                speaker: 'Kavya',\n                text: 'Kya chahiye tumhe?',\n                choices: [\n                    {\n                        id: 'compliment_intelligence',\n                        text: 'Compliment her intelligence',\n                        hinglishText: 'Tumhari intelligence kaafi impressive hai, Kavya',\n                        playerStatEffects: {\n                            charisma: 1\n                        },\n                        targetStatEffects: {\n                            affection: 5,\n                            trust: 3\n                        },\n                        requirements: {}\n                    },\n                    {\n                        id: 'mention_exam',\n                        text: 'Mention her exam performance',\n                        hinglishText: 'Last exam mein tumhara performance... interesting tha',\n                        playerStatEffects: {\n                            manipulation: 1\n                        },\n                        targetStatEffects: {\n                            fear: 10,\n                            trust: -5\n                        },\n                        requirements: {\n                            intelligence: 5\n                        }\n                    },\n                    {\n                        id: 'stare_silently',\n                        text: 'Just stare silently',\n                        hinglishText: '*Chup chaap dekhte raho*',\n                        playerStatEffects: {\n                            dominance: 1\n                        },\n                        targetStatEffects: {\n                            fear: 5,\n                            lust: 2\n                        },\n                        requirements: {}\n                    }\n                ],\n                nextNode: 'chapter_end'\n            },\n            {\n                id: 'chapter_end',\n                speaker: 'System',\n                text: 'FIRST CONTACT ESTABLISHED. ANALYZING TARGET RESPONSE... CHAPTER 1 COMPLETE. THE SERPENT HAS AWAKENED.',\n                choices: [\n                    {\n                        id: 'continue_story',\n                        text: 'Continue to next scene...',\n                        hinglishText: 'Aage ki kahani...',\n                        playerStatEffects: {},\n                        targetStatEffects: {},\n                        requirements: {}\n                    }\n                ],\n                autoAdvance: false\n            }\n        ],\n        rewards: {\n            lustPoints: 10,\n            dominationPoints: 5\n        }\n    },\n    {\n        id: 'second_encounter',\n        title: 'Building Influence',\n        description: 'Vikram\\'s second interaction with Kavya',\n        background: 'dps_classroom',\n        characters: [\n            'kavya'\n        ],\n        unlockConditions: {\n            intelligence: 6\n        },\n        dialogueTree: [\n            {\n                id: 'second_start',\n                speaker: 'Narrator',\n                text: 'The next day, Kavya seems different when she sees Vikram approaching. Her reaction depends on yesterday\\'s encounter...',\n                choices: [],\n                autoAdvance: true,\n                nextNode: 'kavya_reaction',\n                conditions: {\n                    manipulation: 1\n                }\n            },\n            {\n                id: 'kavya_reaction',\n                speaker: 'Kavya',\n                text: 'Tum... tum phir aa gaye?',\n                choices: [\n                    {\n                        id: 'reassure',\n                        text: 'Reassure her gently',\n                        hinglishText: 'Arre, dar kyun rahi ho? Main sirf baat karna chahta hun',\n                        playerStatEffects: {\n                            charisma: 1\n                        },\n                        targetStatEffects: {\n                            fear: -3,\n                            trust: 5,\n                            affection: 3\n                        },\n                        requirements: {}\n                    },\n                    {\n                        id: 'exploit_nervousness',\n                        text: 'Exploit her nervousness',\n                        hinglishText: 'Nervous lag rahi ho, Kavya. Koi baat chhupane ki zarurat nahi',\n                        playerStatEffects: {\n                            manipulation: 2,\n                            dominance: 1\n                        },\n                        targetStatEffects: {\n                            fear: 8,\n                            corruption: 5\n                        },\n                        requirements: {\n                            manipulation: 3\n                        }\n                    },\n                    {\n                        id: 'ask_interests',\n                        text: 'Ask about her interests',\n                        hinglishText: 'Tumhe kya pasand hai karne mein? Studies ke alawa?',\n                        playerStatEffects: {\n                            charisma: 1\n                        },\n                        targetStatEffects: {\n                            affection: 5,\n                            trust: 3\n                        },\n                        requirements: {}\n                    }\n                ],\n                nextNode: 'building_rapport'\n            },\n            {\n                id: 'building_rapport',\n                speaker: 'Narrator',\n                text: 'As the conversation continues, Vikram can feel the dynamic shifting. The System whispers guidance in his mind...',\n                choices: [\n                    {\n                        id: 'suggest_study',\n                        text: 'Suggest studying together',\n                        hinglishText: 'Agar chahiye toh saath mein padh sakte hain kabhi',\n                        playerStatEffects: {\n                            intelligence: 1\n                        },\n                        targetStatEffects: {\n                            affection: 3,\n                            trust: 5\n                        },\n                        requirements: {\n                            intelligence: 4\n                        }\n                    },\n                    {\n                        id: 'comment_appearance',\n                        text: 'Comment on her appearance',\n                        hinglishText: 'Aaj kuch alag lag rahi ho... accha lag raha hai',\n                        playerStatEffects: {\n                            charisma: 1\n                        },\n                        targetStatEffects: {\n                            lust: 3,\n                            affection: 2\n                        },\n                        requirements: {\n                            charisma: 3\n                        }\n                    },\n                    {\n                        id: 'subtle_threat',\n                        text: 'Make a subtle threat',\n                        hinglishText: 'Tumhe pata hai, main bahut kuch jaanta hun... interesting cheezein',\n                        playerStatEffects: {\n                            manipulation: 2,\n                            dominance: 1\n                        },\n                        targetStatEffects: {\n                            fear: 12,\n                            corruption: 3,\n                            trust: -8\n                        },\n                        requirements: {\n                            manipulation: 4,\n                            dominance: 2\n                        }\n                    }\n                ],\n                nextNode: 'scene_end'\n            },\n            {\n                id: 'scene_end',\n                speaker: 'System',\n                text: 'TARGET RESPONSE ANALYZED. RELATIONSHIP PARAMETERS UPDATED. CONTINUE BUILDING INFLUENCE...',\n                choices: [\n                    {\n                        id: 'end_scene',\n                        text: 'End scene',\n                        hinglishText: 'Scene khatam',\n                        playerStatEffects: {},\n                        targetStatEffects: {},\n                        requirements: {}\n                    }\n                ],\n                autoAdvance: false\n            }\n        ],\n        rewards: {\n            lustPoints: 15,\n            dominationPoints: 10\n        }\n    }\n];\nconst chapter1Quests = [\n    {\n        id: 'analyze_kavya',\n        title: 'Analyze Kavya',\n        description: 'Learn about Kavya\\'s weaknesses and desires',\n        targetCharacter: 'kavya',\n        objectives: [\n            'Scan Kavya using the System',\n            'Discover her insecurities',\n            'Make first contact'\n        ],\n        completed: false,\n        active: true,\n        rewards: {\n            lustPoints: 25,\n            dominationPoints: 15\n        }\n    },\n    {\n        id: 'build_influence',\n        title: 'Build Influence',\n        description: 'Continue interacting with targets to build your power',\n        objectives: [\n            'Have multiple conversations with Kavya',\n            'Increase at least one of her stats by 10 points',\n            'Unlock new dialogue options'\n        ],\n        completed: false,\n        active: false,\n        rewards: {\n            lustPoints: 50,\n            dominationPoints: 30\n        }\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/data/chapter1.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/stores/game-store.ts":
/*!**************************************!*\
  !*** ./src/lib/stores/game-store.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGameStore: () => (/* binding */ useGameStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst initialGameState = {\n    player: {\n        name: 'Vikram Singh',\n        stats: {\n            charisma: 5,\n            intelligence: 7,\n            manipulation: 3,\n            wealth: 2,\n            dominance: 1\n        },\n        lustPoints: 0,\n        dominationPoints: 0,\n        inventory: [],\n        location: 'dps_classroom',\n        mood: 'Calculating',\n        time: '10:30 AM'\n    },\n    characters: {\n        kavya: {\n            id: 'kavya',\n            name: 'Kavya Gupta',\n            description: 'Popular girl in class, thinks she\\'s untouchable',\n            stats: {\n                lust: 0,\n                corruption: 0,\n                affection: 0,\n                fear: 0,\n                trust: 0\n            },\n            secrets: [\n                'Has a crush on the cricket captain',\n                'Cheated on last exam'\n            ],\n            dominated: false,\n            profileRevealed: false,\n            relationship: 'stranger'\n        }\n    },\n    progress: {\n        currentChapter: 1,\n        currentScene: 'intro',\n        unlockedScenes: [\n            'intro'\n        ],\n        completedQuests: [],\n        discoveredSecrets: [],\n        achievements: []\n    },\n    settings: {\n        textSpeed: 50,\n        autoAdvance: false,\n        skipSeen: false,\n        volume: {\n            master: 80,\n            music: 70,\n            sfx: 80,\n            voice: 90\n        }\n    },\n    activeQuests: [],\n    shop: []\n};\nconst useGameStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialGameState,\n        // Player actions\n        updatePlayerStats: (stats)=>set((state)=>({\n                    player: {\n                        ...state.player,\n                        stats: {\n                            ...state.player.stats,\n                            ...stats\n                        }\n                    }\n                })),\n        addLustPoints: (points)=>set((state)=>({\n                    player: {\n                        ...state.player,\n                        lustPoints: state.player.lustPoints + points\n                    }\n                })),\n        addDominationPoints: (points)=>set((state)=>({\n                    player: {\n                        ...state.player,\n                        dominationPoints: state.player.dominationPoints + points\n                    }\n                })),\n        setPlayerLocation: (location)=>set((state)=>({\n                    player: {\n                        ...state.player,\n                        location\n                    }\n                })),\n        setPlayerMood: (mood)=>set((state)=>({\n                    player: {\n                        ...state.player,\n                        mood\n                    }\n                })),\n        setPlayerTime: (time)=>set((state)=>({\n                    player: {\n                        ...state.player,\n                        time\n                    }\n                })),\n        // Character actions\n        updateCharacterStats: (characterId, stats)=>set((state)=>({\n                    characters: {\n                        ...state.characters,\n                        [characterId]: {\n                            ...state.characters[characterId],\n                            stats: {\n                                ...state.characters[characterId].stats,\n                                ...stats\n                            }\n                        }\n                    }\n                })),\n        revealCharacterProfile: (characterId)=>set((state)=>({\n                    characters: {\n                        ...state.characters,\n                        [characterId]: {\n                            ...state.characters[characterId],\n                            profileRevealed: true\n                        }\n                    }\n                })),\n        dominateCharacter: (characterId)=>set((state)=>({\n                    characters: {\n                        ...state.characters,\n                        [characterId]: {\n                            ...state.characters[characterId],\n                            dominated: true,\n                            relationship: 'dominated'\n                        }\n                    }\n                })),\n        updateCharacterRelationship: (characterId, relationship)=>set((state)=>({\n                    characters: {\n                        ...state.characters,\n                        [characterId]: {\n                            ...state.characters[characterId],\n                            relationship\n                        }\n                    }\n                })),\n        // Dialogue actions\n        startDialogue: (nodeId, characterId, sceneId = 'current')=>set(()=>({\n                    currentDialogue: {\n                        nodeId,\n                        characterId,\n                        sceneId\n                    }\n                })),\n        endDialogue: ()=>set(()=>({\n                    currentDialogue: undefined\n                })),\n        makeChoice: (choice, targetCharacterId)=>{\n            const state = get();\n            // Apply player stat effects\n            if (Object.keys(choice.playerStatEffects).length > 0) {\n                state.updatePlayerStats(choice.playerStatEffects);\n            }\n            // Apply target stat effects\n            if (targetCharacterId && Object.keys(choice.targetStatEffects).length > 0) {\n                state.updateCharacterStats(targetCharacterId, choice.targetStatEffects);\n            }\n        },\n        // Quest actions\n        addQuest: (quest)=>set((state)=>({\n                    activeQuests: [\n                        ...state.activeQuests,\n                        quest\n                    ]\n                })),\n        completeQuest: (questId)=>set((state)=>({\n                    activeQuests: state.activeQuests.map((quest)=>quest.id === questId ? {\n                            ...quest,\n                            completed: true,\n                            active: false\n                        } : quest),\n                    progress: {\n                        ...state.progress,\n                        completedQuests: [\n                            ...state.progress.completedQuests,\n                            questId\n                        ]\n                    }\n                })),\n        updateQuestObjectives: (questId, objectives)=>set((state)=>({\n                    activeQuests: state.activeQuests.map((quest)=>quest.id === questId ? {\n                            ...quest,\n                            objectives\n                        } : quest)\n                })),\n        // Progress actions\n        unlockScene: (sceneId)=>set((state)=>({\n                    progress: {\n                        ...state.progress,\n                        unlockedScenes: [\n                            ...new Set([\n                                ...state.progress.unlockedScenes,\n                                sceneId\n                            ])\n                        ]\n                    }\n                })),\n        setCurrentScene: (sceneId)=>set((state)=>({\n                    progress: {\n                        ...state.progress,\n                        currentScene: sceneId\n                    }\n                })),\n        addAchievement: (achievementId)=>set((state)=>({\n                    progress: {\n                        ...state.progress,\n                        achievements: [\n                            ...new Set([\n                                ...state.progress.achievements,\n                                achievementId\n                            ])\n                        ]\n                    }\n                })),\n        // Settings actions\n        updateSettings: (settings)=>set((state)=>({\n                    settings: {\n                        ...state.settings,\n                        ...settings\n                    }\n                })),\n        // Game actions\n        resetGame: ()=>set(()=>({\n                    ...initialGameState\n                })),\n        saveGame: ()=>{\n            // This will be handled by the persist middleware\n            console.log('Game saved!');\n        }\n    }), {\n    name: 'vikram-game-storage',\n    partialize: (state)=>({\n            player: state.player,\n            characters: state.characters,\n            progress: state.progress,\n            settings: state.settings,\n            activeQuests: state.activeQuests\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/stores/game-store.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canMakeChoice: () => (/* binding */ canMakeChoice),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatStatValue: () => (/* binding */ formatStatValue),\n/* harmony export */   getLocationName: () => (/* binding */ getLocationName),\n/* harmony export */   getRelationshipColor: () => (/* binding */ getRelationshipColor),\n/* harmony export */   getStatColor: () => (/* binding */ getStatColor)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatStatValue(value) {\n    return Math.max(0, Math.min(100, value)).toString();\n}\nfunction getStatColor(value) {\n    if (value >= 80) return 'text-red-500';\n    if (value >= 60) return 'text-orange-500';\n    if (value >= 40) return 'text-yellow-500';\n    if (value >= 20) return 'text-blue-500';\n    return 'text-gray-500';\n}\nfunction getRelationshipColor(relationship) {\n    switch(relationship){\n        case 'dominated':\n            return 'text-purple-500';\n        case 'corrupted':\n            return 'text-red-500';\n        case 'interested':\n            return 'text-pink-500';\n        case 'friend':\n            return 'text-green-500';\n        case 'acquaintance':\n            return 'text-blue-500';\n        default:\n            return 'text-gray-500';\n    }\n}\nfunction canMakeChoice(requirements, playerStats) {\n    return Object.entries(requirements).every(([stat, required])=>playerStats[stat] >= required);\n}\nfunction getLocationName(location) {\n    const locationNames = {\n        'dps_classroom': 'DPS Classroom',\n        'dps_library': 'DPS Library',\n        'dps_cafeteria': 'DPS Cafeteria',\n        'dps_principal_office': 'Principal\\'s Office',\n        'dps_corridor': 'School Corridor',\n        'home': 'Home',\n        'market': 'Local Market',\n        'park': 'Nehru Park'\n    };\n    return locationNames[location] || location;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRU8sU0FBU0MsZ0JBQWdCQyxLQUFhO0lBQzNDLE9BQU9DLEtBQUtDLEdBQUcsQ0FBQyxHQUFHRCxLQUFLRSxHQUFHLENBQUMsS0FBS0gsUUFBUUksUUFBUTtBQUNuRDtBQUVPLFNBQVNDLGFBQWFMLEtBQWE7SUFDeEMsSUFBSUEsU0FBUyxJQUFJLE9BQU87SUFDeEIsSUFBSUEsU0FBUyxJQUFJLE9BQU87SUFDeEIsSUFBSUEsU0FBUyxJQUFJLE9BQU87SUFDeEIsSUFBSUEsU0FBUyxJQUFJLE9BQU87SUFDeEIsT0FBTztBQUNUO0FBRU8sU0FBU00scUJBQXFCQyxZQUFvQjtJQUN2RCxPQUFRQTtRQUNOLEtBQUs7WUFBYSxPQUFPO1FBQ3pCLEtBQUs7WUFBYSxPQUFPO1FBQ3pCLEtBQUs7WUFBYyxPQUFPO1FBQzFCLEtBQUs7WUFBVSxPQUFPO1FBQ3RCLEtBQUs7WUFBZ0IsT0FBTztRQUM1QjtZQUFTLE9BQU87SUFDbEI7QUFDRjtBQUVPLFNBQVNDLGNBQWNDLFlBQW9DLEVBQUVDLFdBQW1DO0lBQ3JHLE9BQU9DLE9BQU9DLE9BQU8sQ0FBQ0gsY0FBY0ksS0FBSyxDQUFDLENBQUMsQ0FBQ0MsTUFBTUMsU0FBUyxHQUN6REwsV0FBVyxDQUFDSSxLQUFLLElBQUlDO0FBRXpCO0FBRU8sU0FBU0MsZ0JBQWdCQyxRQUFnQjtJQUM5QyxNQUFNQyxnQkFBd0M7UUFDNUMsaUJBQWlCO1FBQ2pCLGVBQWU7UUFDZixpQkFBaUI7UUFDakIsd0JBQXdCO1FBQ3hCLGdCQUFnQjtRQUNoQixRQUFRO1FBQ1IsVUFBVTtRQUNWLFFBQVE7SUFDVjtJQUNBLE9BQU9BLGFBQWEsQ0FBQ0QsU0FBUyxJQUFJQTtBQUNwQyIsInNvdXJjZXMiOlsiRDpcXGlkbGVzY2FwZXJcXHZpa3JhbS1nYW1lXFxzcmNcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRTdGF0VmFsdWUodmFsdWU6IG51bWJlcik6IHN0cmluZyB7XG4gIHJldHVybiBNYXRoLm1heCgwLCBNYXRoLm1pbigxMDAsIHZhbHVlKSkudG9TdHJpbmcoKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFN0YXRDb2xvcih2YWx1ZTogbnVtYmVyKTogc3RyaW5nIHtcbiAgaWYgKHZhbHVlID49IDgwKSByZXR1cm4gJ3RleHQtcmVkLTUwMCc7XG4gIGlmICh2YWx1ZSA+PSA2MCkgcmV0dXJuICd0ZXh0LW9yYW5nZS01MDAnO1xuICBpZiAodmFsdWUgPj0gNDApIHJldHVybiAndGV4dC15ZWxsb3ctNTAwJztcbiAgaWYgKHZhbHVlID49IDIwKSByZXR1cm4gJ3RleHQtYmx1ZS01MDAnO1xuICByZXR1cm4gJ3RleHQtZ3JheS01MDAnO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0UmVsYXRpb25zaGlwQ29sb3IocmVsYXRpb25zaGlwOiBzdHJpbmcpOiBzdHJpbmcge1xuICBzd2l0Y2ggKHJlbGF0aW9uc2hpcCkge1xuICAgIGNhc2UgJ2RvbWluYXRlZCc6IHJldHVybiAndGV4dC1wdXJwbGUtNTAwJztcbiAgICBjYXNlICdjb3JydXB0ZWQnOiByZXR1cm4gJ3RleHQtcmVkLTUwMCc7XG4gICAgY2FzZSAnaW50ZXJlc3RlZCc6IHJldHVybiAndGV4dC1waW5rLTUwMCc7XG4gICAgY2FzZSAnZnJpZW5kJzogcmV0dXJuICd0ZXh0LWdyZWVuLTUwMCc7XG4gICAgY2FzZSAnYWNxdWFpbnRhbmNlJzogcmV0dXJuICd0ZXh0LWJsdWUtNTAwJztcbiAgICBkZWZhdWx0OiByZXR1cm4gJ3RleHQtZ3JheS01MDAnO1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjYW5NYWtlQ2hvaWNlKHJlcXVpcmVtZW50czogUmVjb3JkPHN0cmluZywgbnVtYmVyPiwgcGxheWVyU3RhdHM6IFJlY29yZDxzdHJpbmcsIG51bWJlcj4pOiBib29sZWFuIHtcbiAgcmV0dXJuIE9iamVjdC5lbnRyaWVzKHJlcXVpcmVtZW50cykuZXZlcnkoKFtzdGF0LCByZXF1aXJlZF0pID0+IFxuICAgIHBsYXllclN0YXRzW3N0YXRdID49IHJlcXVpcmVkXG4gICk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRMb2NhdGlvbk5hbWUobG9jYXRpb246IHN0cmluZyk6IHN0cmluZyB7XG4gIGNvbnN0IGxvY2F0aW9uTmFtZXM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgJ2Rwc19jbGFzc3Jvb20nOiAnRFBTIENsYXNzcm9vbScsXG4gICAgJ2Rwc19saWJyYXJ5JzogJ0RQUyBMaWJyYXJ5JyxcbiAgICAnZHBzX2NhZmV0ZXJpYSc6ICdEUFMgQ2FmZXRlcmlhJyxcbiAgICAnZHBzX3ByaW5jaXBhbF9vZmZpY2UnOiAnUHJpbmNpcGFsXFwncyBPZmZpY2UnLFxuICAgICdkcHNfY29ycmlkb3InOiAnU2Nob29sIENvcnJpZG9yJyxcbiAgICAnaG9tZSc6ICdIb21lJyxcbiAgICAnbWFya2V0JzogJ0xvY2FsIE1hcmtldCcsXG4gICAgJ3BhcmsnOiAnTmVocnUgUGFyaycsXG4gIH07XG4gIHJldHVybiBsb2NhdGlvbk5hbWVzW2xvY2F0aW9uXSB8fCBsb2NhdGlvbjtcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIiwiZm9ybWF0U3RhdFZhbHVlIiwidmFsdWUiLCJNYXRoIiwibWF4IiwibWluIiwidG9TdHJpbmciLCJnZXRTdGF0Q29sb3IiLCJnZXRSZWxhdGlvbnNoaXBDb2xvciIsInJlbGF0aW9uc2hpcCIsImNhbk1ha2VDaG9pY2UiLCJyZXF1aXJlbWVudHMiLCJwbGF5ZXJTdGF0cyIsIk9iamVjdCIsImVudHJpZXMiLCJldmVyeSIsInN0YXQiLCJyZXF1aXJlZCIsImdldExvY2F0aW9uTmFtZSIsImxvY2F0aW9uIiwibG9jYXRpb25OYW1lcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/@react-spring","vendor-chunks/zustand","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cidlescaper%5Cvikram-game%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidlescaper%5Cvikram-game&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();